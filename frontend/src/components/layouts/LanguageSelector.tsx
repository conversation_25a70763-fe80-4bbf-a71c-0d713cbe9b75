"use client"

import React, { useTransition } from "react"
import Image from "next/image"
import { Check } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Locale, locales } from "@/i18n/config"
import { setLocale } from "@/i18n/services"

const LanguageSelector = ({ locale }: { locale: string }) => {
  const [isPending, startTransition] = useTransition()

  const handleChangeLanguage = (value: string) => {
    const locale = value as Locale
    startTransition(() => setLocale(locale))
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="text-muted-foreground h-auto bg-transparent! p-0! underline ring-0! outline-none!"
          title="Language"
          disabled={isPending}
        >
          <Image
            className="shrink-0 rounded-full border object-cover group-data-[collapsible=icon]:size-6"
            src={`/images/language-${locale}.png`}
            alt={locale}
            width={20}
            height={20}
          />

          <span className="uppercase group-data-[collapsible=icon]:hidden">
            {locale}
          </span>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="min-w-28"
        side="top"
        align="start"
        sideOffset={4}
      >
        {locales.map((l) => (
          <DropdownMenuItem
            key={l}
            className="uppercase"
            onClick={() => handleChangeLanguage(l)}
          >
            <Image
              className="rounded-full border object-cover"
              src={`/images/language-${l}.png`}
              alt={l}
              width={16}
              height={16}
            />
            {l}
            {locale === l && <Check className="ml-auto" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default LanguageSelector
