import React from "react"
import Image from "next/image"
import Link from "next/link"

import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON>ontent,
  Sidebar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarRail,
} from "@/components/ui/sidebar"
import CurrencySelector from "@/components/layouts/CurrencySelector"
import LanguageSwitcher from "@/components/layouts/LanguageSwitcher"
import NavMain from "@/components/layouts/NavMain"
import NavUser from "@/components/layouts/NavUser"

const AppSidebar = ({ ...props }: React.ComponentProps<typeof Sidebar>) => (
  <Sidebar collapsible="icon" {...props}>
    <SidebarHeader>
      <SidebarMenu className="items-start p-2 pb-0">
        <Link href="/">
          <Image
            className="flex h-7 w-auto object-contain group-data-[collapsible=icon]:hidden"
            src="/logo-blue.png"
            alt="Singapore Medical Group"
            width={512}
            height={175}
            priority
          />
          <Image
            className="hidden h-7 w-[15px] object-contain group-data-[collapsible=icon]:flex"
            src="/logo-blue-small.png"
            alt="Singapore Medical Group"
            width={120}
            height={120}
            priority
          />
        </Link>
      </SidebarMenu>
    </SidebarHeader>

    <SidebarContent className="overflow-x-hidden">
      <NavMain />
    </SidebarContent>

    <SidebarFooter>
      <div className="flex items-start gap-4 overflow-hidden p-2 group-data-[collapsible=icon]:flex-col group-data-[collapsible=icon]:px-1">
        <LanguageSwitcher />
        <CurrencySelector />
      </div>

      <NavUser />
    </SidebarFooter>

    <SidebarRail />
  </Sidebar>
)

export default AppSidebar
