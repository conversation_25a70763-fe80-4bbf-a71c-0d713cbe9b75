"use client"

import React, { useState } from "react"
import { useTranslations } from "next-intl"

import { CustomResponse } from "@/types/response"
import { useRouter } from "@/hooks/use-router"
import { LoaderButton } from "@/components/ui/loader-button"
import Google from "@/icons/Google"
import api from "@/services/api"

const GoogleOAuthButton = () => {
  const { push } = useRouter()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const t = useTranslations("auth.signIn")

  const handleClick = async () => {
    try {
      setIsLoading(true)
      const {
        data: {
          data: { message },
        },
      } = await api.get<CustomResponse<{ message: string }>>(
        "/oauth-provider/google-oauth/v1/authorize"
      )

      push(message)
    } catch (error) {
      console.error("Authorization error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <LoaderButton
      size="lg"
      className="w-full"
      onClick={handleClick}
      isLoading={isLoading}
      icon={Google}
    >
      {t("continueWithGoogle")}
    </LoaderButton>
  )
}

export default GoogleOAuthButton
