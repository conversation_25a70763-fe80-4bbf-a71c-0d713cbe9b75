"use server"

import { cookies } from "next/headers"

import { defaultLocale, Locale } from "@/i18n/config"

const COOKIE_NAME = "NEXT_LOCALE"

export const getLocale = async (): Promise<Locale> => {
  const cookieStore = await cookies()
  const locale = cookieStore.get(COOKIE_NAME)?.value
  return (locale ?? defaultLocale) as Locale
}

export const setLocale = async (locale: Locale) => {
  const cookieStore = await cookies()
  cookieStore.set(COOKIE_NAME, locale)
}
