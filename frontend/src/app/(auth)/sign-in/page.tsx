import React from "react"
import { getTranslations } from "next-intl/server"

import GoogleOAuthButton from "@/components/auth/GoogleOAuthButton"
import MicrosoftOAuthButton from "@/components/auth/MicrosoftOAuthButton"

export async function generateMetadata() {
  const t = await getTranslations("auth.signIn")
  return {
    title: t("pageTitle"),
  }
}

const SignIn = async () => {
  const t = await getTranslations("auth.signIn")

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col items-center gap-1 text-center">
        <h1 className="text-2xl font-bold">{t("title")}</h1>
        <p className="text-muted-foreground text-sm">{t("description")}</p>
      </div>

      <GoogleOAuthButton />
      <MicrosoftOAuthButton />
    </div>
  )
}

export default SignIn
