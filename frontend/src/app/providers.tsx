import React from "react"
import { NextIntlClientProvider } from "next-intl"
import TopLoader from "nextjs-toploader"
import { NuqsAdapter } from "nuqs/adapters/next/app"

import { Toaster } from "@/components/ui/sonner"
import Preloader from "@/components/layouts/Preloader"

interface ProvidersProps {
  children: React.ReactNode
  locale: string
  messages: any
}

const Providers = ({ children, locale, messages }: ProvidersProps) => (
  <NextIntlClientProvider locale={locale} messages={messages}>
    <NuqsAdapter>
      <Preloader />

      <TopLoader
        height={2}
        color="var(--primary)"
        shadow={false}
        showSpinner={false}
        showForHashAnchor={false}
      />

      {children}

      <Toaster />
    </NuqsAdapter>
  </NextIntlClientProvider>
)

export default Providers
