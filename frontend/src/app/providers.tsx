import React from "react"
import { NextIntlClientProvider } from "next-intl"
import TopLoader from "nextjs-toploader"
import { NuqsAdapter } from "nuqs/adapters/next/app"

import { Toaster } from "@/components/ui/sonner"
import Preloader from "@/components/layouts/Preloader"

const Providers = ({ children }: { children: React.ReactNode }) => (
  <NextIntlClientProvider>
    <NuqsAdapter>
      <Preloader />

      <TopLoader
        height={2}
        color="var(--primary)"
        shadow={false}
        showSpinner={false}
        showForHashAnchor={false}
      />

      {children}

      <Toaster />
    </NuqsAdapter>
  </NextIntlClientProvider>
)

export default Providers
