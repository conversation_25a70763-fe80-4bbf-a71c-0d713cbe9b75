import type { Metadata } from "next"
import { Source_Sans_3 as FontSans } from "next/font/google"

import { cn } from "@/lib/utils"
import Providers from "@/app/providers"
import { getLocale } from "@/i18n/services"

import "@/app/globals.css"

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
})

export const metadata: Metadata = {
  title: {
    template: "%s | OneFlow | Singapore Medical Group",
    default: "OneFlow | Singapore Medical Group",
  },
  description:
    "Streamline your healthcare workflow with Singapore Medical Group's integrated platform.",
}

const RootLayout = async ({
  children,
}: Readonly<{
  children: React.ReactNode
}>) => {
  const locale = await getLocale()
  const messages = (await import(`../../messages/${locale}.json`)).default

  return (
    <html lang={locale}>
      <body
        className={cn(
          "relative flex min-h-svh flex-col overflow-x-hidden overscroll-y-none font-sans text-pretty antialiased",
          fontSans.variable
        )}
      >
        <Providers locale={locale} messages={messages}>
          {children}
        </Providers>
      </body>
    </html>
  )
}

export default RootLayout
