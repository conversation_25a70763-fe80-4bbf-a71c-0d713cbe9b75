from rest_framework.viewsets import ModelViewSet
from datetime import datetime, date
from collections import defaultdict
from django.db.models import (
    DecimalField,
    Sum,
    Value,
    Q,
    Count,
    OuterRef,
    Subquery,
    DateField,
    F,
    ExpressionWrapper,
    IntegerField,
)
from django.db.models.functions import Coalesce, ExtractYear
from rest_framework.decorators import action
from rest_framework import status
from fuzzywuzzy import fuzz, process
import re
import traceback
import pandas as pd
import numpy as np
import calendar
from decimal import Decimal
from accounting.models import (
    ChartofAccount,
    JournalEntryTransaction,
    Clinic,
    Employee,
    Patients,
    Appointments,
    PatientMKT,
    SalesOrder,
)
import django.db.models as models
from summary.models import Commentary, FinancialTransactionSnapshot
from utils.response_template import custom_error_response, custom_success_response
from summary.helpers import get_conversion_rate

FTE_WEIGHTS = {
    "Permanent Full Time": 1.0,
    "Permanent Part Time": 0.5,
    "Casual Workers": 0.3,
    "Intern": 0.2,
    "Visting Consultant": 0.4,
    "Contract Basis": 0.5,
}


class SummaryViewSet(ModelViewSet):
    @action(detail=False, methods=["get"], url_path="filters")
    def filters(self, request):
        try:
            segments = (
                Clinic.objects.exclude(segment__in=["Corporate", "Others"])
                .values_list("segment", flat=True)
                .distinct()
                .order_by("segment")
            )

            segments = list(segments)

            if Clinic.objects.filter(segment="Others").exists():
                segments.append("Others")

            clinics = (
                Clinic.objects.exclude(name="Ceased Clinic / Codes")
                .values_list("code", flat=True)
                .distinct()
                .order_by("code")
            )

            return custom_success_response(
                {
                    "segments": segments,
                    "clinics": list(clinics),
                }
            )
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )


class ProfitAndLossViewSet(ModelViewSet):
    @action(detail=False, methods=["get"], url_path="overview")
    def overview(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            chart_years = list(range(start_year, end_year + 1))

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset for revenue
            revenue_queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="revenue",
                year__range=[start_year, end_year],
            )

            # Apply segment filter if provided
            if segments:
                revenue_queryset = revenue_queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                revenue_queryset = revenue_queryset.filter(clinic__code__in=clinics)

            # Build base queryset for profit before tax (used for EBITDA and Net Profit)
            pbt_queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="profit_before_tax",
                year__range=[start_year, end_year],
            )

            # Apply segment filter if provided
            if segments:
                pbt_queryset = pbt_queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                pbt_queryset = pbt_queryset.filter(clinic__code__in=clinics)

            # Get accounts for bank debt and cash balance calculations
            accounts = ChartofAccount.objects.filter(
                account_type__in=["Liability", "Asset"],
            ).values(
                "id",
                "account_type",
                "account_name",
                "cash_account",
                "bank_debt",
                "total_debt",
            )

            bank_debt_ids = set()
            cash_ids = set()

            for a in accounts:
                if a["bank_debt"]:
                    bank_debt_ids.add(a["id"])
                if a["cash_account"]:
                    cash_ids.add(a["id"])

            # Build base queryset for bank debt entries
            bank_debt_queryset = JournalEntryTransaction.objects.filter(
                chart_of_account_id__in=bank_debt_ids,
                transaction_date__year__gte=start_year,
                transaction_date__year__lte=end_year,
            ).exclude(memo__icontains="FOR CLOSING")

            # Apply segment filter if provided
            if segments:
                clinic_codes = Clinic.objects.filter(segment__in=segments).values_list(
                    "code", flat=True
                )
                bank_debt_queryset = bank_debt_queryset.filter(
                    chart_of_account__clinic_code__in=clinic_codes
                )

            # Apply clinic filter if provided
            if clinics:
                bank_debt_queryset = bank_debt_queryset.filter(
                    chart_of_account__clinic_code__in=clinics
                )

            # Build base queryset for cash balance entries
            cash_queryset = JournalEntryTransaction.objects.filter(
                chart_of_account_id__in=cash_ids,
                transaction_date__year__gte=start_year,
                transaction_date__year__lte=end_year,
            ).exclude(memo__icontains="FOR CLOSING")

            # Apply segment filter if provided
            if segments:
                clinic_codes = Clinic.objects.filter(segment__in=segments).values_list(
                    "code", flat=True
                )
                cash_queryset = cash_queryset.filter(
                    chart_of_account__clinic_code__in=clinic_codes
                )

            # Apply clinic filter if provided
            if clinics:
                cash_queryset = cash_queryset.filter(
                    chart_of_account__clinic_code__in=clinics
                )

            historical_data = {}
            for calc_year in chart_years:
                historical_data[calc_year] = {
                    "gross_revenue": 0.0,
                    "ebitda": 0.0,
                    "net_profit": 0.0,
                    "bank_debt": 0.0,
                    "cash_balance": 0.0,
                    "fte": 0.0,
                }

            # Get revenue data from FinancialTransactionSnapshot
            revenue_entries = revenue_queryset.values("year").annotate(
                total_amount=Sum("amount")
            )

            for entry in revenue_entries:
                year = entry["year"]
                if year in historical_data:
                    historical_data[year]["gross_revenue"] = (
                        float(entry["total_amount"]) * conversion_rate
                    )

            # Get profit before tax data from FinancialTransactionSnapshot (used for both EBITDA and Net Profit)
            pbt_entries = pbt_queryset.values("year").annotate(
                total_amount=Sum("amount")
            )

            for entry in pbt_entries:
                year = entry["year"]
                if year in historical_data:
                    amount = float(entry["total_amount"]) * conversion_rate
                    # Use profit_before_tax for both EBITDA and Net Profit
                    historical_data[year]["ebitda"] = amount
                    historical_data[year]["net_profit"] = amount

            # Get bank debt data from JournalEntryTransaction (as this is not in snapshot)
            bank_debt_entries = (
                bank_debt_queryset.annotate(year=ExtractYear("transaction_date"))
                .values("year")
                .annotate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
            )

            for entry in bank_debt_entries:
                year = entry["year"]
                if year in historical_data:
                    credit = float(entry["total_credit"]) * conversion_rate
                    debit = float(entry["total_debit"]) * conversion_rate
                    historical_data[year]["bank_debt"] += credit - debit

            # Get cash balance data from JournalEntryTransaction (as this is not in snapshot)
            cash_balance_entries = (
                cash_queryset.annotate(year=ExtractYear("transaction_date"))
                .values("year")
                .annotate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
            )

            for entry in cash_balance_entries:
                year = entry["year"]
                if year in historical_data:
                    credit = float(entry["total_credit"]) * conversion_rate
                    debit = float(entry["total_debit"]) * conversion_rate
                    historical_data[year]["cash_balance"] += debit - credit

            # Calculate FTE for each year with segment and clinic filters
            for calc_year in chart_years:
                snapshot_date = datetime(calc_year, 12, 31).date()

                # Get all employees for the year
                employees = Employee.objects.filter(
                    Q(joined_date__lte=snapshot_date)
                    & (
                        Q(resignation_date__isnull=True)
                        | Q(resignation_date__gt=snapshot_date)
                    )
                ).only(
                    "department",
                    "category",
                    "joined_date",
                    "resignation_date",
                    "clinic_name",
                )

                # Build clinic segment mapping for filtering (same as fte_demographics_chart)
                clinic_segment_map = {}
                valid_segments = set()

                if segments or clinics:
                    # Get clinic name → segment map
                    clinic_qs = Clinic.objects.exclude(
                        name="Ceased Clinic / Codes"
                    ).values("name", "segment", "code")
                    clinic_segment_map = {
                        normalize_clinic_name(c["name"]): c["segment"] or "Others"
                        for c in clinic_qs
                    }

                    # Build sets of valid segments and clinic codes
                    if segments:
                        valid_segments.update(segments)
                    if clinics:
                        # Also get segments for these clinic codes
                        for clinic in clinic_qs:
                            if clinic["code"] in clinics:
                                valid_segments.add(clinic["segment"] or "Others")

                total_fte = 0.0
                for emp in employees:
                    # Apply segment/clinic filter if specified
                    if segments or clinics:
                        emp_clinic_name = emp.clinic_name or ""
                        # Use the same logic as fte_demographics_chart to match clinic to segment
                        employee_segment = match_clinic_segment(
                            emp_clinic_name, clinic_segment_map
                        )

                        # Check if this employee's segment/clinic matches the filter
                        segment_matches = (
                            not segments or employee_segment in valid_segments
                        )

                        # For clinic filter, check if employee's clinic matches any specified clinic codes
                        clinic_matches = True
                        if clinics:
                            clinic_matches = False
                            normalized_clinic_name = normalize_clinic_name(
                                emp_clinic_name
                            )
                            for clinic_code in clinics:
                                try:
                                    clinic_obj = Clinic.objects.get(code=clinic_code)
                                    if (
                                        normalize_clinic_name(clinic_obj.name)
                                        == normalized_clinic_name
                                    ):
                                        clinic_matches = True
                                        break
                                except Clinic.DoesNotExist:
                                    continue

                        if not (segment_matches and clinic_matches):
                            continue

                    # Calculate FTE value for this employee
                    emp_type = (emp.category or "").strip()
                    fte_value = FTE_WEIGHTS.get(emp_type, 0.0)
                    total_fte += fte_value

                historical_data[calc_year]["fte"] = total_fte

            # Currency conversion is already applied in the calculations above

            def calc_change(current, previous):
                return float((current - previous) / previous * 100) if previous else 0.0

            current_data = historical_data.get(
                year,
                {
                    "gross_revenue": 0.0,
                    "ebitda": 0.0,
                    "net_profit": 0.0,
                    "bank_debt": 0.0,
                    "cash_balance": 0.0,
                    "fte": 0.0,
                },
            )
            prev_data = historical_data.get(year - 1, {})

            debt_ratio = (
                (current_data["bank_debt"] / current_data["cash_balance"] * 100)
                if current_data["cash_balance"] > 0
                else 0
            )
            cash_ratio = (
                (current_data["cash_balance"] / current_data["bank_debt"] * 100)
                if current_data["bank_debt"] > 0
                else 100
            )

            def chart(key):
                return [
                    {"name": str(y), "value": round(historical_data[y][key], 0)}
                    for y in chart_years
                ]

            overview_data = {
                "gross_revenue": {
                    "value": round(current_data["gross_revenue"], 0),
                    "percentage": round(
                        calc_change(
                            current_data["gross_revenue"],
                            prev_data.get("gross_revenue", 0),
                        ),
                        1,
                    ),
                    "chart_data": chart("gross_revenue"),
                },
                "ebitda": {
                    "value": round(current_data["ebitda"], 0),
                    "percentage": round(
                        calc_change(current_data["ebitda"], prev_data.get("ebitda", 0)),
                        1,
                    ),
                    "chart_data": chart("ebitda"),
                },
                "bank_debt": {
                    "value": round(current_data["bank_debt"], 0),
                    "percentage": round(
                        calc_change(
                            current_data["bank_debt"], prev_data.get("bank_debt", 0)
                        ),
                        1,
                    ),
                    "chart_data": chart("bank_debt"),
                    "additional_info": {
                        "label": "Debt Ratio",
                        "value": f"{debt_ratio:.1f}pts",
                    },
                },
                "cash_balance": {
                    "value": round(current_data["cash_balance"], 0),
                    "percentage": round(
                        calc_change(
                            current_data["cash_balance"],
                            prev_data.get("cash_balance", 0),
                        ),
                        1,
                    ),
                    "chart_data": chart("cash_balance"),
                    "additional_info": {
                        "label": "Cash Ratio",
                        "value": f"{cash_ratio:.1f}pts",
                    },
                },
                "fte": {
                    "value": round(current_data["fte"], 0),
                    "percentage": round(
                        calc_change(current_data["fte"], prev_data.get("fte", 0)), 1
                    ),
                    "chart_data": chart("fte"),
                },
                "net_profit": {
                    "value": round(current_data["net_profit"], 0),
                    "percentage": round(
                        calc_change(
                            current_data["net_profit"], prev_data.get("net_profit", 0)
                        ),
                        1,
                    ),
                    "chart_data": chart("net_profit"),
                },
            }

            return custom_success_response(overview_data)

        except Exception as e:
            import traceback

            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="drill-down")
    def pnl_drill_down(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset for revenue
            revenue_queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="revenue",
                year__range=[start_year, end_year],
                amount__gt=0,  # TODO: Need to remove this
            )

            # Apply segment filter if provided
            if segments:
                revenue_queryset = revenue_queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                revenue_queryset = revenue_queryset.filter(clinic__code__in=clinics)

            # Revenue
            revenue_entries = revenue_queryset.select_related(
                "clinic", "chart_of_account"
            ).values(
                "year",
                "month",
                "financial_statement_type",
                "clinic__name",
                "clinic__segment",
                "chart_of_account__account_name",
                "amount",
            )

            # Build base queryset for profit before tax
            pbt_queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="profit_before_tax",
                year__range=[start_year, end_year],
            )

            # Apply segment filter if provided
            if segments:
                pbt_queryset = pbt_queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                pbt_queryset = pbt_queryset.filter(clinic__code__in=clinics)

            # Profit before tax (used for both EBITDA and Net Profit)
            # TODO: Separate out EBITDA and Net Profit when the data is available
            pbt_entries = pbt_queryset.select_related(
                "clinic", "chart_of_account"
            ).values(
                "year",
                "month",
                "financial_statement_type",
                "clinic__name",
                "clinic__segment",
                "chart_of_account__account_name",
                "amount",
            )

            # Combine everything
            all_entries = list(revenue_entries) + list(pbt_entries)

            results_map = {}

            for row in all_entries:
                key = (
                    row["year"],
                    row["month"],
                    row["clinic__name"],
                    row["clinic__segment"],
                    row.get("chart_of_account__account_name"),
                )

                if key not in results_map:
                    results_map[key] = {
                        "year": row["year"],
                        "month": row["month"],
                        "clinic": row["clinic__name"],
                        "segment": row["clinic__segment"],
                        "account": row.get("chart_of_account__account_name"),
                        "period": f"{row['year']}-{int(row['month']):02d}",
                        "revenue": 0,
                        "ebitda": 0,
                        "net_profit": 0,
                    }

                converted_amount = float(row["amount"]) * conversion_rate

                if row.get("financial_statement_type") == "revenue":
                    results_map[key]["revenue"] += converted_amount
                else:
                    # Use profit_before_tax for both EBITDA and Net Profit
                    results_map[key]["ebitda"] += converted_amount
                    results_map[key]["net_profit"] += converted_amount

            results = list(results_map.values())
            return custom_success_response(results)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="revenue/chart")
    def revenue_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset
            queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="revenue",
                year__range=[start_year, end_year],
                amount__gt=0,  # TODO: Need to remove this
            )

            # Apply segment filter if provided
            if segments:
                queryset = queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                queryset = queryset.filter(clinic__code__in=clinics)

            # Get chart of accounts for Revenues/Income
            revenue_entries = (
                queryset.values("year").annotate(amount=Sum("amount")).order_by("year")
            )

            # Convert the queryset into df
            revenue_df = pd.DataFrame(list(revenue_entries))

            # Convert amount column type to float and apply currency conversion
            revenue_df["amount"] = revenue_df["amount"].astype(float) * conversion_rate

            # Calculate the percentage change between years by creating a new column and do a shift down by 1
            revenue_df["past_year_amount"] = revenue_df["amount"].shift(1)
            revenue_df["past_year_amount"].fillna(0, inplace=True)
            revenue_df["percentage"] = revenue_df.apply(
                lambda x: (
                    round(
                        (x["amount"] - x["past_year_amount"])
                        / x["past_year_amount"]
                        * 100,
                        1,
                    )
                    if x["past_year_amount"] != 0
                    else 0
                ),
                axis=1,
            )

            # Drop the temporary past_year_amount column
            revenue_df.drop(columns=["past_year_amount"], inplace=True)

            revenue_df["name"] = revenue_df["year"].astype(str)
            revenue_df.drop(columns=["year"], inplace=True)

            # Convert the df into a dictionary
            revenue_dict = revenue_df.to_dict(orient="records")

            # Get the commentary
            commentary = (
                Commentary.objects.filter(chart_name="Revenue Breakdown Chart")
                .order_by("-created_at")
                .first()
            )

            if commentary:
                commentary = commentary.commentary
            else:
                commentary = ""

            data = {"chart_data": revenue_dict, "commentary": commentary}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="revenue/with-sma/chart")
    def revenue_with_sma_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            agg_type = request.query_params.get("type", "Month Year")

            # Normalize type
            if agg_type.lower() in ["month", "month year", "monthly"]:
                agg_type = "month"
            elif agg_type.lower() in ["year", "yearly"]:
                agg_type = "year"
            elif agg_type.lower() in ["quarter", "quarterly"]:
                agg_type = "quarter"
            else:
                agg_type = "month"  # fallback

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            qs = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="revenue",
                year__range=[start_year, end_year],
                amount__gt=0,  # TODO: remove this filter if negative revenue is valid
            )

            # Apply segment filter if provided
            if segments:
                qs = qs.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                qs = qs.filter(clinic__code__in=clinics)

            if agg_type == "year":
                revenue_entries = (
                    qs.values("year").annotate(amount=Sum("amount")).order_by("year")
                )
            elif agg_type == "quarter":
                revenue_entries = (
                    qs.annotate(
                        quarter=ExpressionWrapper(
                            ((F("month") - 1) / 3) + 1, output_field=IntegerField()
                        )
                    )
                    .values("year", "quarter")
                    .annotate(amount=Sum("amount"))
                    .order_by("year", "quarter")
                )
            else:  # month
                revenue_entries = (
                    qs.values("year", "month")
                    .annotate(amount=Sum("amount"))
                    .order_by("year", "month")
                )

            # Convert to DataFrame
            revenue_df = pd.DataFrame(list(revenue_entries))

            if revenue_df.empty:
                return custom_success_response({"chart_data": [], "commentary": ""})

            # Apply currency conversion to amount column
            revenue_df["amount"] = revenue_df["amount"].astype(float) * conversion_rate

            # Add name column depending on type
            if agg_type == "year":
                revenue_df = revenue_df.sort_values(by=["year"]).reset_index(drop=True)
                revenue_df["name"] = revenue_df["year"].astype(int).astype(str)
            elif agg_type == "quarter":
                revenue_df = revenue_df.sort_values(by=["year", "quarter"]).reset_index(
                    drop=True
                )
                revenue_df["name"] = revenue_df.apply(
                    lambda x: f"Q{int(x['quarter'])} {str(int(x['year']))[-2:]}", axis=1
                )
            else:  # month
                revenue_df = revenue_df.sort_values(by=["year", "month"]).reset_index(
                    drop=True
                )
                revenue_df["name"] = revenue_df.apply(
                    lambda x: f"{calendar.month_abbr[int(x['month'])]} {str(int(x['year']))[-2:]}",
                    axis=1,
                )

            # Compute previous period percentage change
            revenue_df["past_amount"] = revenue_df["amount"].shift(1)
            revenue_df["percentage"] = revenue_df.apply(
                lambda x: (
                    round((x["amount"] - x["past_amount"]) / x["past_amount"] * 100, 1)
                    if x["past_amount"] not in [0, None]
                    else None
                ),
                axis=1,
            )

            # Compute SMA3 and SMA6 over periods (months/quarters/years)
            revenue_df["sma3"] = revenue_df["amount"].rolling(window=3).mean()
            revenue_df["sma6"] = revenue_df["amount"].rolling(window=6).mean()

            # Keep only the needed columns
            keep_cols = ["name", "amount", "year", "percentage", "sma3", "sma6"]
            if agg_type == "quarter":
                keep_cols.insert(2, "quarter")
            elif agg_type == "month":
                keep_cols.insert(2, "month")

            revenue_df = revenue_df[keep_cols]

            # Convert numeric columns to float
            num_cols = ["amount", "percentage", "sma3", "sma6"]
            revenue_df[num_cols] = revenue_df[num_cols].astype(float)

            # Replace NaN with None for JSON serialization
            revenue_df = revenue_df.replace({np.nan: None})

            revenue_dict = revenue_df.to_dict(orient="records")

            # Get the commentary
            commentary = (
                Commentary.objects.filter(chart_name="Revenue Breakdown Chart")
                .order_by("-created_at")
                .first()
            )
            commentary = commentary.commentary if commentary else ""

            data = {"chart_data": revenue_dict, "commentary": commentary}
            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="revenue/drill-down")
    def revenue_drill_down(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset
            queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="revenue",
                year__range=[start_year, end_year],
                amount__gt=0,  # TODO: Need to remove this
            )

            # Apply segment filter if provided
            if segments:
                queryset = queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                queryset = queryset.filter(clinic__code__in=clinics)

            # Get chart of accounts for Revenues/Income
            revenue_entries = (
                queryset.select_related("clinic", "chart_of_account")
                .values(
                    "year",
                    "month",
                    "clinic__name",
                    "clinic__segment",
                    "chart_of_account__account_name",
                    "amount",
                )
                .order_by("year", "month", "clinic__name")
            )

            results = list(revenue_entries)

            for row in results:
                row["clinic"] = row.pop("clinic__name")
                row["segment"] = row.pop("clinic__segment")
                row["account"] = row.pop("chart_of_account__account_name")
                row["period"] = f"{row['year']}-{int(row['month']):02d}"
                # Apply currency conversion
                row["amount"] = float(row["amount"]) * conversion_rate

            return custom_success_response(results)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="ebitda/chart")
    def ebitda_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset
            queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="profit_before_tax",
                year__range=[start_year, end_year],
            )

            # Apply segment filter if provided
            if segments:
                queryset = queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                queryset = queryset.filter(clinic__code__in=clinics)

            # Get chart of accounts for Revenues/Income
            profit_before_tax_entries = (
                queryset.values("year").annotate(amount=Sum("amount")).order_by("year")
            )

            # Convert the queryset into df
            profit_before_tax_df = pd.DataFrame(list(profit_before_tax_entries))

            # Convert amount column type to float and apply currency conversion
            profit_before_tax_df["amount"] = (
                profit_before_tax_df["amount"].astype(float) * conversion_rate
            )

            # Calculate the percentage change between years by creating a new column and do a shift down by 1
            profit_before_tax_df["past_year_amount"] = profit_before_tax_df[
                "amount"
            ].shift(1)
            profit_before_tax_df["past_year_amount"].fillna(0, inplace=True)
            profit_before_tax_df["percentage"] = profit_before_tax_df.apply(
                lambda x: (
                    round(
                        (x["amount"] - x["past_year_amount"])
                        / x["past_year_amount"]
                        * 100,
                        1,
                    )
                    if x["past_year_amount"] != 0
                    else 0
                ),
                axis=1,
            )

            # Drop the temporary past_year_amount column
            profit_before_tax_df.drop(columns=["past_year_amount"], inplace=True)

            profit_before_tax_df["name"] = profit_before_tax_df["year"].astype(str)
            profit_before_tax_df.drop(columns=["year"], inplace=True)

            # Convert the df into a dictionary
            profit_before_tax_dict = profit_before_tax_df.to_dict(orient="records")

            # Get the commentary
            commentary = (
                Commentary.objects.filter(chart_name="Profit Before Tax Chart")
                .order_by("-created_at")
                .first()
            )

            if commentary:
                commentary = commentary.commentary
            else:
                commentary = ""

            data = {"chart_data": profit_before_tax_dict, "commentary": commentary}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="ebitda/with-sma/chart")
    def ebitda_with_sma_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            agg_type = request.query_params.get("type", "Month Year")

            # Normalize type
            if agg_type.lower() in ["month", "month year", "monthly"]:
                agg_type = "month"
            elif agg_type.lower() in ["year", "yearly"]:
                agg_type = "year"
            elif agg_type.lower() in ["quarter", "quarterly"]:
                agg_type = "quarter"
            else:
                agg_type = "month"  # fallback

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            qs = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="profit_before_tax",
                year__range=[start_year, end_year],
            )

            # Apply segment filter if provided
            if segments:
                qs = qs.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                qs = qs.filter(clinic__code__in=clinics)

            if agg_type == "year":
                ebitda_entries = (
                    qs.values("year").annotate(amount=Sum("amount")).order_by("year")
                )
            elif agg_type == "quarter":
                ebitda_entries = (
                    qs.annotate(
                        quarter=ExpressionWrapper(
                            ((F("month") - 1) / 3) + 1, output_field=IntegerField()
                        )
                    )
                    .values("year", "quarter")
                    .annotate(amount=Sum("amount"))
                    .order_by("year", "quarter")
                )
            else:  # month
                ebitda_entries = (
                    qs.values("year", "month")
                    .annotate(amount=Sum("amount"))
                    .order_by("year", "month")
                )

            # Convert to DataFrame
            ebitda_df = pd.DataFrame(list(ebitda_entries))

            if ebitda_df.empty:
                return custom_success_response({"chart_data": [], "commentary": ""})

            # Apply currency conversion to amount column
            ebitda_df["amount"] = ebitda_df["amount"].astype(float) * conversion_rate

            # Add name column depending on type
            if agg_type == "year":
                ebitda_df = ebitda_df.sort_values(by=["year"]).reset_index(drop=True)
                ebitda_df["name"] = ebitda_df["year"].astype(int).astype(str)
            elif agg_type == "quarter":
                ebitda_df = ebitda_df.sort_values(by=["year", "quarter"]).reset_index(
                    drop=True
                )
                ebitda_df["name"] = ebitda_df.apply(
                    lambda x: f"Q{int(x['quarter'])} {str(int(x['year']))[-2:]}", axis=1
                )
            else:  # month
                ebitda_df = ebitda_df.sort_values(by=["year", "month"]).reset_index(
                    drop=True
                )
                ebitda_df["name"] = ebitda_df.apply(
                    lambda x: f"{calendar.month_abbr[int(x['month'])]} {str(int(x['year']))[-2:]}",
                    axis=1,
                )

            # Compute previous period percentage change
            ebitda_df["past_amount"] = ebitda_df["amount"].shift(1)
            ebitda_df["percentage"] = ebitda_df.apply(
                lambda x: (
                    round((x["amount"] - x["past_amount"]) / x["past_amount"] * 100, 1)
                    if x["past_amount"] not in [0, None]
                    else None
                ),
                axis=1,
            )

            # Compute SMA3 and SMA6 over periods (months/quarters/years)
            ebitda_df["sma3"] = ebitda_df["amount"].rolling(window=3).mean()
            ebitda_df["sma6"] = ebitda_df["amount"].rolling(window=6).mean()

            # Keep only the needed columns
            keep_cols = ["name", "amount", "year", "percentage", "sma3", "sma6"]
            if agg_type == "quarter":
                keep_cols.insert(2, "quarter")
            elif agg_type == "month":
                keep_cols.insert(2, "month")

            ebitda_df = ebitda_df[keep_cols]

            # Convert numeric columns to float
            num_cols = ["amount", "percentage", "sma3", "sma6"]
            ebitda_df[num_cols] = ebitda_df[num_cols].astype(float)

            # Replace NaN with None for JSON serialization
            ebitda_df = ebitda_df.replace({np.nan: None})

            ebitda_dict = ebitda_df.to_dict(orient="records")

            # Get the commentary
            commentary = (
                Commentary.objects.filter(chart_name="Profit Before Tax Chart")
                .order_by("-created_at")
                .first()
            )
            commentary = commentary.commentary if commentary else ""

            data = {"chart_data": ebitda_dict, "commentary": commentary}
            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="ebitda/drill-down")
    def ebitda_drill_down(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset
            queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="profit_before_tax",
                year__range=[start_year, end_year],
            )

            # Apply segment filter if provided
            if segments:
                queryset = queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                queryset = queryset.filter(clinic__code__in=clinics)

            # Get chart of accounts for profit before tax
            profit_before_tax_entries = (
                queryset.select_related("clinic", "chart_of_account")
                .values(
                    "year",
                    "month",
                    "clinic__name",
                    "clinic__segment",
                    "chart_of_account__account_name",
                    "amount",
                )
                .order_by("year", "month", "clinic__name")
            )

            results = list(profit_before_tax_entries)

            for row in results:
                row["clinic"] = row.pop("clinic__name")
                row["segment"] = row.pop("clinic__segment")
                row["account"] = row.pop("chart_of_account__account_name")
                row["period"] = f"{row['year']}-{int(row['month']):02d}"
                # Apply currency conversion
                row["amount"] = float(row["amount"]) * conversion_rate

            return custom_success_response(results)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="net-profit/chart")
    def net_profit_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset
            queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="profit_before_tax",
                year__range=[start_year, end_year],
            )

            # Apply segment filter if provided
            if segments:
                queryset = queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                queryset = queryset.filter(clinic__code__in=clinics)

            # Get chart of accounts for Revenues/Income
            profit_before_tax_entries = (
                queryset.values("year").annotate(amount=Sum("amount")).order_by("year")
            )

            # Convert the queryset into df
            profit_before_tax_df = pd.DataFrame(list(profit_before_tax_entries))

            # Convert amount column type to float and apply currency conversion
            profit_before_tax_df["amount"] = (
                profit_before_tax_df["amount"].astype(float) * conversion_rate
            )

            # Calculate the percentage change between years by creating a new column and do a shift down by 1
            profit_before_tax_df["past_year_amount"] = profit_before_tax_df[
                "amount"
            ].shift(1)
            profit_before_tax_df["past_year_amount"].fillna(0, inplace=True)
            profit_before_tax_df["percentage"] = profit_before_tax_df.apply(
                lambda x: (
                    round(
                        (x["amount"] - x["past_year_amount"])
                        / x["past_year_amount"]
                        * 100,
                        1,
                    )
                    if x["past_year_amount"] != 0
                    else 0
                ),
                axis=1,
            )

            # Drop the temporary past_year_amount column
            profit_before_tax_df.drop(columns=["past_year_amount"], inplace=True)

            profit_before_tax_df["name"] = profit_before_tax_df["year"].astype(str)
            profit_before_tax_df.drop(columns=["year"], inplace=True)

            # Convert the df into a dictionary
            profit_before_tax_dict = profit_before_tax_df.to_dict(orient="records")

            # Get the commentary
            commentary = (
                Commentary.objects.filter(chart_name="Profit Before Tax Chart")
                .order_by("-created_at")
                .first()
            )

            if commentary:
                commentary = commentary.commentary
            else:
                commentary = ""

            data = {"chart_data": profit_before_tax_dict, "commentary": commentary}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="profit/with-sma/chart")
    def profit_with_sma_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            agg_type = request.query_params.get("type", "Month Year")
            chart_type = request.query_params.get("chart_type", "Net Profit")

            # Normalize type
            if agg_type.lower() in ["month", "month year", "monthly"]:
                agg_type = "month"
            elif agg_type.lower() in ["year", "yearly"]:
                agg_type = "year"
            elif agg_type.lower() in ["quarter", "quarterly"]:
                agg_type = "quarter"
            else:
                agg_type = "month"  # fallback

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            qs = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="profit_before_tax",  # TODO: Follow given chart_type: Net Profit / Operating Profit
                year__range=[start_year, end_year],
            )

            # Apply segment filter if provided
            if segments:
                qs = qs.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                qs = qs.filter(clinic__code__in=clinics)

            if agg_type == "year":
                profit_entries = (
                    qs.values("year").annotate(amount=Sum("amount")).order_by("year")
                )
            elif agg_type == "quarter":
                profit_entries = (
                    qs.annotate(
                        quarter=ExpressionWrapper(
                            ((F("month") - 1) / 3) + 1, output_field=IntegerField()
                        )
                    )
                    .values("year", "quarter")
                    .annotate(amount=Sum("amount"))
                    .order_by("year", "quarter")
                )
            else:  # month
                profit_entries = (
                    qs.values("year", "month")
                    .annotate(amount=Sum("amount"))
                    .order_by("year", "month")
                )

            # Convert to DataFrame
            profit_df = pd.DataFrame(list(profit_entries))

            if profit_df.empty:
                return custom_success_response({"chart_data": [], "commentary": ""})

            # Apply currency conversion to amount column
            profit_df["amount"] = profit_df["amount"].astype(float) * conversion_rate

            # Add name column depending on type
            if agg_type == "year":
                profit_df = profit_df.sort_values(by=["year"]).reset_index(drop=True)
                profit_df["name"] = profit_df["year"].astype(int).astype(str)
            elif agg_type == "quarter":
                profit_df = profit_df.sort_values(by=["year", "quarter"]).reset_index(
                    drop=True
                )
                profit_df["name"] = profit_df.apply(
                    lambda x: f"Q{int(x['quarter'])} {str(int(x['year']))[-2:]}", axis=1
                )
            else:  # month
                profit_df = profit_df.sort_values(by=["year", "month"]).reset_index(
                    drop=True
                )
                profit_df["name"] = profit_df.apply(
                    lambda x: f"{calendar.month_abbr[int(x['month'])]} {str(int(x['year']))[-2:]}",
                    axis=1,
                )

            # Compute previous period percentage change
            profit_df["past_amount"] = profit_df["amount"].shift(1)
            profit_df["percentage"] = profit_df.apply(
                lambda x: (
                    round((x["amount"] - x["past_amount"]) / x["past_amount"] * 100, 1)
                    if x["past_amount"] not in [0, None]
                    else None
                ),
                axis=1,
            )

            # Compute SMA3 and SMA6 over periods (months/quarters/years)
            profit_df["sma3"] = profit_df["amount"].rolling(window=3).mean()
            profit_df["sma6"] = profit_df["amount"].rolling(window=6).mean()

            # Keep only the needed columns
            keep_cols = ["name", "amount", "year", "percentage", "sma3", "sma6"]
            if agg_type == "quarter":
                keep_cols.insert(2, "quarter")
            elif agg_type == "month":
                keep_cols.insert(2, "month")

            profit_df = profit_df[keep_cols]

            # Convert numeric columns to float
            num_cols = ["amount", "percentage", "sma3", "sma6"]
            profit_df[num_cols] = profit_df[num_cols].astype(float)

            # Replace NaN with None for JSON serialization
            profit_df = profit_df.replace({np.nan: None})

            profit_dict = profit_df.to_dict(orient="records")

            # Get the commentary
            commentary = (
                Commentary.objects.filter(chart_name="Profit Before Tax Chart")
                .order_by("-created_at")
                .first()
            )
            commentary = commentary.commentary if commentary else ""

            data = {"chart_data": profit_dict, "commentary": commentary}
            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="net-profit/drill-down")
    def net_profit_drill_down(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset
            queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="profit_before_tax",
                year__range=[start_year, end_year],
            )

            # Apply segment filter if provided
            if segments:
                queryset = queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                queryset = queryset.filter(clinic__code__in=clinics)

            # Get chart of accounts for profit before tax
            profit_before_tax_entries = (
                queryset.select_related("clinic", "chart_of_account")
                .values(
                    "year",
                    "month",
                    "clinic__name",
                    "clinic__segment",
                    "chart_of_account__account_name",
                    "amount",
                )
                .order_by("year", "month", "clinic__name")
            )

            results = list(profit_before_tax_entries)

            for row in results:
                row["clinic"] = row.pop("clinic__name")
                row["segment"] = row.pop("clinic__segment")
                row["account"] = row.pop("chart_of_account__account_name")
                row["period"] = f"{row['year']}-{int(row['month']):02d}"
                # Apply currency conversion
                row["amount"] = float(row["amount"]) * conversion_rate

            return custom_success_response(results)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(
        detail=False, methods=["get"], url_path="revenue-ebitda-profit-margin/chart"
    )
    def revenue_ebitda_profit_margin_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            today = datetime.now()

            end_year = int(request.query_params.get("year", today.year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            current_month = today.month

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # =====================================================
            # 1) Yearly totals
            # =====================================================

            # Build base queryset for revenue
            revenue_queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="revenue",
                year__range=[start_year, end_year],
                amount__gt=0,
            )

            # Apply segment filter if provided
            if segments:
                revenue_queryset = revenue_queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                revenue_queryset = revenue_queryset.filter(clinic__code__in=clinics)

            revenue_entries = (
                revenue_queryset.values("year")
                .annotate(amount=Sum("amount"))
                .order_by("year")
            )
            revenue_df = pd.DataFrame(list(revenue_entries))

            # Build base queryset for EBITDA
            ebitda_queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="profit_before_tax",
                year__range=[start_year, end_year],
            )

            # Apply segment filter if provided
            if segments:
                ebitda_queryset = ebitda_queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                ebitda_queryset = ebitda_queryset.filter(clinic__code__in=clinics)

            ebitda_entries = (
                ebitda_queryset.values("year")
                .annotate(line_amount=Sum("amount"))
                .order_by("year")
            )
            ebitda_df = pd.DataFrame(list(ebitda_entries))

            if revenue_df.empty:
                revenue_df = pd.DataFrame(columns=["year", "amount"])
            if ebitda_df.empty:
                ebitda_df = pd.DataFrame(columns=["year", "line_amount"])

            # Apply currency conversion
            if not revenue_df.empty:
                revenue_df["amount"] = (
                    revenue_df["amount"].astype(float) * conversion_rate
                )
            if not ebitda_df.empty:
                ebitda_df["line_amount"] = (
                    ebitda_df["line_amount"].astype(float) * conversion_rate
                )

            merged_df = pd.merge(revenue_df, ebitda_df, on="year", how="left").fillna(0)

            merged_df["percentage"] = merged_df.apply(
                lambda x: (
                    round((x["line_amount"] / x["amount"]) * 100, 1)
                    if x["amount"] != 0
                    else 0
                ),
                axis=1,
            )
            merged_df["SG"] = merged_df["amount"]

            chart_1_df = merged_df.copy()
            chart_1_df["name"] = chart_1_df["year"].astype(str)
            chart_1_df.drop(columns=["year"], inplace=True)
            chart_1_data = chart_1_df.to_dict(orient="records")

            # =====================================================
            # 2) YTD totals (up to current month for each year)
            # =====================================================

            # Build base queryset for YTD revenue
            revenue_ytd_queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="revenue",
                year__range=[start_year, end_year],
                month__lte=current_month,
                amount__gt=0,
            )

            # Apply segment filter if provided
            if segments:
                revenue_ytd_queryset = revenue_ytd_queryset.filter(
                    clinic__segment__in=segments
                )

            # Apply clinic filter if provided
            if clinics:
                revenue_ytd_queryset = revenue_ytd_queryset.filter(
                    clinic__code__in=clinics
                )

            revenue_entries_ytd = (
                revenue_ytd_queryset.values("year")
                .annotate(amount=Sum("amount"))
                .order_by("year")
            )
            revenue_ytd_df = pd.DataFrame(list(revenue_entries_ytd))

            # Build base queryset for YTD EBITDA
            ebitda_ytd_queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="profit_before_tax",
                year__range=[start_year, end_year],
                month__lte=current_month,
            )

            # Apply segment filter if provided
            if segments:
                ebitda_ytd_queryset = ebitda_ytd_queryset.filter(
                    clinic__segment__in=segments
                )

            # Apply clinic filter if provided
            if clinics:
                ebitda_ytd_queryset = ebitda_ytd_queryset.filter(
                    clinic__code__in=clinics
                )

            ebitda_entries_ytd = (
                ebitda_ytd_queryset.values("year")
                .annotate(line_amount=Sum("amount"))
                .order_by("year")
            )
            ebitda_ytd_df = pd.DataFrame(list(ebitda_entries_ytd))

            if revenue_ytd_df.empty:
                revenue_ytd_df = pd.DataFrame(columns=["year", "amount"])
            if ebitda_ytd_df.empty:
                ebitda_ytd_df = pd.DataFrame(columns=["year", "line_amount"])

            # Apply currency conversion
            if not revenue_ytd_df.empty:
                revenue_ytd_df["amount"] = (
                    revenue_ytd_df["amount"].astype(float) * conversion_rate
                )
            if not ebitda_ytd_df.empty:
                ebitda_ytd_df["line_amount"] = (
                    ebitda_ytd_df["line_amount"].astype(float) * conversion_rate
                )

            merged_ytd_df = pd.merge(
                revenue_ytd_df, ebitda_ytd_df, on="year", how="left"
            ).fillna(0)

            merged_ytd_df["percentage"] = merged_ytd_df.apply(
                lambda x: (
                    round((x["line_amount"] / x["amount"]) * 100, 1)
                    if x["amount"] != 0
                    else 0
                ),
                axis=1,
            )
            merged_ytd_df["SG"] = merged_ytd_df["amount"]

            chart_2_df = merged_ytd_df.copy()
            chart_2_df["name"] = chart_2_df["year"].apply(lambda y: f"YTD {y}")
            chart_2_df.drop(columns=["year"], inplace=True)
            chart_2_data = chart_2_df.to_dict(orient="records")

            # =====================================================
            # 3) Commentary (reuse from revenue chart)
            # =====================================================
            commentary_obj = (
                Commentary.objects.filter(chart_name="Revenue Breakdown Chart")
                .order_by("-created_at")
                .first()
            )
            commentary = commentary_obj.commentary if commentary_obj else ""

            # =====================================================
            # 4) Final response
            # =====================================================
            data = {
                "chart_1_data": chart_1_data,
                "chart_2_data": chart_2_data,
                "commentary": commentary,
            }

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(
        detail=False, methods=["get"], url_path="revenue-breakdown/by-segment/chart"
    )
    def revenue_breakdown_by_segment_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset
            queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="revenue",
                year__gte=start_year,
                year__lte=end_year,
                amount__gt=0,  # TODO: Need to remove this
            )

            # Apply segment filter if provided
            if segments:
                queryset = queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                queryset = queryset.filter(clinic__code__in=clinics)

            # Get revenue entries
            revenue_entries = (
                queryset.select_related("clinic")
                .values("year", "clinic__segment")
                .annotate(amount=models.Sum(models.F("amount")))
                .order_by("year")
            )

            # Convert the queryset into df
            revenue_df = pd.DataFrame(list(revenue_entries))

            # Apply currency conversion to amount column
            if not revenue_df.empty:
                revenue_df["amount"] = (
                    revenue_df["amount"].astype(float) * conversion_rate
                )

            # Create pivot table with segments as columns
            if revenue_df.empty:
                # Return empty response when no data
                return custom_success_response({"chart_data": []})

            pivot_df = revenue_df.pivot_table(
                index="year",
                columns="clinic__segment",
                values="amount",
                aggfunc="sum",
                fill_value=0,
            ).reset_index()

            pivot_df["total"] = pivot_df.drop(columns=["year"]).sum(axis=1)
            pivot_df.rename(columns={"year": "name"}, inplace=True)

            # Convert all columns except for "year" to float and name to str
            pivot_df["name"] = pivot_df["name"].astype(str)
            for col in pivot_df.columns:
                if col != "name":
                    pivot_df[col] = pivot_df[col].astype(float)

            formatted_data = pivot_df.to_dict(orient="records")

            # Get the commentary
            commentary = (
                Commentary.objects.filter(chart_name="Revenue Breakdown Chart")
                .order_by("-created_at")
                .first()
            )

            if commentary:
                commentary = commentary.commentary
            else:
                commentary = ""

            data = {"chart_data": formatted_data, "commentary": commentary}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="revenue-breakdown/by-clinic/chart")
    def revenue_breakdown_by_clinic_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset
            queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="revenue",
                year__gte=start_year,
                year__lte=end_year,
                amount__gt=0,  # TODO: Need to remove this
            )

            # Apply segment filter if provided
            if segments:
                queryset = queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                queryset = queryset.filter(clinic__code__in=clinics)

            # Get chart of accounts for Revenues/Income
            revenue_entries = (
                queryset.select_related("clinic")
                .values("year", "clinic__name")
                .annotate(amount=models.Sum(models.F("amount")))
                .order_by("year")
            )

            # Convert the queryset into df
            revenue_df = pd.DataFrame(list(revenue_entries))

            # Apply currency conversion to amount column
            if not revenue_df.empty:
                revenue_df["amount"] = (
                    revenue_df["amount"].astype(float) * conversion_rate
                )

            # Create pivot table with segments as columns
            if revenue_df.empty:
                # Return empty response when no data
                return custom_success_response({"chart_data": []})

            pivot_df = revenue_df.pivot_table(
                index="year",
                columns="clinic__name",
                values="amount",
                aggfunc="sum",
                fill_value=0,
            ).reset_index()

            pivot_df["total"] = pivot_df.drop(columns=["year"]).sum(axis=1)
            pivot_df.rename(columns={"year": "name"}, inplace=True)

            # Convert all columns except for "year" to float and name to str
            pivot_df["name"] = pivot_df["name"].astype(str)
            for col in pivot_df.columns:
                if col != "name":
                    pivot_df[col] = pivot_df[col].astype(float)

            formatted_data = pivot_df.to_dict(orient="records")

            # Get the commentary
            commentary = (
                Commentary.objects.filter(chart_name="Revenue Breakdown Chart")
                .order_by("-created_at")
                .first()
            )

            if commentary:
                commentary = commentary.commentary
            else:
                commentary = ""

            data = {"chart_data": formatted_data, "commentary": commentary}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="revenue/segment-breakdown/chart")
    def revenue_segment_breakdown_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset
            queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="revenue",
                year__gte=start_year,
                year__lte=end_year,
                amount__gt=0,  # TODO: Need to remove this
            ).exclude(clinic__segment="Corporate")

            # Apply segment filter if provided
            if segments:
                queryset = queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                queryset = queryset.filter(clinic__code__in=clinics)

            # Get chart of accounts for Revenues/Income
            revenue_entries = (
                queryset.select_related("clinic")
                .values("year", "clinic__segment")
                .annotate(amount=models.Sum(models.F("amount")))
                .order_by("year")
            )

            # Convert the queryset into df
            revenue_df = pd.DataFrame(list(revenue_entries))

            if revenue_df.empty:
                formatted_data = []
            else:
                # Apply currency conversion to amount column
                revenue_df["amount"] = (
                    revenue_df["amount"].astype(float) * conversion_rate
                )

                # Create pivot table with segments as columns
                pivot_df = revenue_df.pivot_table(
                    index="year",
                    columns="clinic__segment",
                    values="amount",
                    aggfunc="sum",
                    fill_value=0,
                ).reset_index()

                # Ensure column types
                pivot_df["year"] = pivot_df["year"].astype(int)
                for col in pivot_df.columns:
                    if col != "year":
                        pivot_df[col] = pivot_df[col].astype(float)

                # Now restructure into desired format
                formatted_data = []
                segments = [col for col in pivot_df.columns if col != "year"]

                for segment in segments:
                    records = []
                    prev_value = None
                    for _, row in pivot_df.iterrows():
                        year = str(int(row["year"]))
                        value = float(row[segment])
                        record = {"name": year, "value": value}

                        if prev_value is not None and prev_value != 0:
                            record["percentage"] = (
                                (value - prev_value) / prev_value
                            ) * 100
                        prev_value = value
                        records.append(record)

                    formatted_data.append({"name": segment, "records": records})

            # Get the commentary
            commentary = (
                Commentary.objects.filter(chart_name="Revenue Breakdown Chart")
                .order_by("-created_at")
                .first()
            )

            if commentary:
                commentary = commentary.commentary
            else:
                commentary = ""

            data = {"chart_data": formatted_data, "commentary": commentary}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="ebitda/segment-breakdown/chart")
    def ebitda_segment_breakdown_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset
            queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="profit_before_tax",
                year__gte=start_year,
                year__lte=end_year,
            ).exclude(clinic__segment="Corporate")

            # Apply segment filter if provided
            if segments:
                queryset = queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                queryset = queryset.filter(clinic__code__in=clinics)

            # Get chart of accounts for Ebitda
            ebitda_entries = (
                queryset.select_related("clinic")
                .values("year", "clinic__segment")
                .annotate(amount=models.Sum(models.F("amount")))
                .order_by("year")
            )

            # Convert the queryset into df
            ebitda_df = pd.DataFrame(list(ebitda_entries))

            if ebitda_df.empty:
                formatted_data = []
            else:
                # Apply currency conversion to amount column
                ebitda_df["amount"] = (
                    ebitda_df["amount"].astype(float) * conversion_rate
                )

                # Create pivot table with segments as columns
                pivot_df = ebitda_df.pivot_table(
                    index="year",
                    columns="clinic__segment",
                    values="amount",
                    aggfunc="sum",
                    fill_value=0,
                ).reset_index()

                # Ensure column types
                pivot_df["year"] = pivot_df["year"].astype(int)
                for col in pivot_df.columns:
                    if col != "year":
                        pivot_df[col] = pivot_df[col].astype(float)

                # Now restructure into desired format
                formatted_data = []
                segments = [col for col in pivot_df.columns if col != "year"]

                for segment in segments:
                    records = []
                    prev_value = None
                    for _, row in pivot_df.iterrows():
                        year = str(int(row["year"]))
                        value = float(row[segment])
                        record = {"name": year, "value": value}

                        if prev_value is not None and prev_value != 0:
                            record["percentage"] = (
                                (value - prev_value) / prev_value
                            ) * 100
                        prev_value = value
                        records.append(record)

                    formatted_data.append({"name": segment, "records": records})

            # Get the commentary
            commentary = (
                Commentary.objects.filter(chart_name="Profit Before Tax Chart")
                .order_by("-created_at")
                .first()
            )

            if commentary:
                commentary = commentary.commentary
            else:
                commentary = ""

            data = {"chart_data": formatted_data, "commentary": commentary}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="profit/segment-breakdown/chart")
    def profit_segment_breakdown_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            chart_type = request.query_params.get("chart_type", "Net Profit")

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset
            queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="profit_before_tax",  # TODO: Follow given chart_type: Net Profit / Operating Profit
                year__gte=start_year,
                year__lte=end_year,
            ).exclude(clinic__segment="Corporate")

            # Apply segment filter if provided
            if segments:
                queryset = queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                queryset = queryset.filter(clinic__code__in=clinics)

            # Get chart of accounts for Profit
            profit_entries = (
                queryset.select_related("clinic")
                .values("year", "clinic__segment")
                .annotate(amount=models.Sum(models.F("amount")))
                .order_by("year")
            )

            # Convert the queryset into df
            profit_df = pd.DataFrame(list(profit_entries))

            if profit_df.empty:
                formatted_data = []
            else:
                # Apply currency conversion to amount column
                profit_df["amount"] = (
                    profit_df["amount"].astype(float) * conversion_rate
                )

                # Create pivot table with segments as columns
                pivot_df = profit_df.pivot_table(
                    index="year",
                    columns="clinic__segment",
                    values="amount",
                    aggfunc="sum",
                    fill_value=0,
                ).reset_index()

                # Ensure column types
                pivot_df["year"] = pivot_df["year"].astype(int)
                for col in pivot_df.columns:
                    if col != "year":
                        pivot_df[col] = pivot_df[col].astype(float)

                # Now restructure into desired format
                formatted_data = []
                segments = [col for col in pivot_df.columns if col != "year"]

                for segment in segments:
                    records = []
                    prev_value = None
                    for _, row in pivot_df.iterrows():
                        year = str(int(row["year"]))
                        value = float(row[segment])
                        record = {"name": year, "value": value}

                        if prev_value is not None and prev_value != 0:
                            record["percentage"] = (
                                (value - prev_value) / prev_value
                            ) * 100
                        prev_value = value
                        records.append(record)

                    formatted_data.append({"name": segment, "records": records})

            # Get the commentary
            commentary = (
                Commentary.objects.filter(chart_name="Profit Before Tax Chart")
                .order_by("-created_at")
                .first()
            )

            if commentary:
                commentary = commentary.commentary
            else:
                commentary = ""

            data = {"chart_data": formatted_data, "commentary": commentary}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )


class CreditAndBalanceSheetViewSet(ModelViewSet):
    @action(detail=False, methods=["get"], url_path="total-debt/chart")
    def total_debt_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Get all relevant debt accounts
            debt_account_ids = ChartofAccount.objects.filter(
                total_debt=True
            ).values_list("id", flat=True)

            # Build base queryset for debt entries
            debt_queryset = JournalEntryTransaction.objects.filter(
                chart_of_account_id__in=debt_account_ids,
                transaction_date__year__gte=start_year,
                transaction_date__year__lte=end_year,
            ).exclude(memo__icontains="FOR CLOSING")

            # Apply segment filter if provided
            if segments:
                clinic_codes = Clinic.objects.filter(segment__in=segments).values_list(
                    "code", flat=True
                )
                debt_queryset = debt_queryset.filter(
                    chart_of_account__clinic_code__in=clinic_codes
                )

            # Apply clinic filter if provided
            if clinics:
                debt_queryset = debt_queryset.filter(
                    chart_of_account__clinic_code__in=clinics
                )

            # Use database aggregation for performance
            debt_entries = debt_queryset.values("transaction_date__year").annotate(
                total_credit=Coalesce(
                    Sum("reporting_credit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
                total_debit=Coalesce(
                    Sum("reporting_debit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
            )

            # Create year => debt value map
            debt_by_year = {
                e["transaction_date__year"]: (
                    float(e["total_credit"]) - float(e["total_debit"])
                )
                * conversion_rate
                for e in debt_entries
            }

            def pct_change(current, previous):
                if previous == 0:
                    return 100.0 if current else 0.0
                return round((current - previous) / previous * 100, 1)

            # Format final response
            response = []
            for y in range(start_year, end_year + 1):
                current = debt_by_year.get(y, 0.0)
                previous = debt_by_year.get(y - 1, 0.0)
                response.append(
                    {
                        "name": str(y),
                        "amount": round(current, 2),
                        "percentage": pct_change(current, previous),
                    }
                )

            data = {"chart_data": response, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="bank-and-net-debt/chart")
    def bank_and_net_debt_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Get account IDs for bank and net debt
            bank_debt_ids = set(
                ChartofAccount.objects.filter(bank_debt=True).values_list(
                    "id", flat=True
                )
            )
            net_debt_ids = set(
                ChartofAccount.objects.filter(total_debt=True).values_list(
                    "id", flat=True
                )
            )

            relevant_ids = bank_debt_ids | net_debt_ids

            # Fetch and group all entries
            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=relevant_ids,
                    transaction_date__year__gte=start_year,
                    transaction_date__year__lte=end_year,
                )
                .exclude(memo__icontains="FOR CLOSING")
                .values("transaction_date__year", "chart_of_account_id")
                .annotate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
            )

            # Aggregate results
            yearly_totals = defaultdict(lambda: {"bankDebt": 0.0, "netDebt": 0.0})

            for entry in entries:
                year_key = entry["transaction_date__year"]
                account_id = entry["chart_of_account_id"]
                net = (
                    float(entry["total_credit"]) - float(entry["total_debit"])
                ) * conversion_rate

                if account_id in bank_debt_ids:
                    yearly_totals[year_key]["bankDebt"] += net
                if account_id in net_debt_ids:
                    yearly_totals[year_key]["netDebt"] += net

            # Final formatted response
            response = []
            for y in range(start_year, end_year + 1):
                response.append(
                    {
                        "name": str(y),
                        "bankDebt": round(yearly_totals[y]["bankDebt"], 2),
                        "netDebt": round(yearly_totals[y]["netDebt"], 2),
                    }
                )

            data = {"chart_data": response, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="current-cash-balance/chart")
    def current_cash_balance_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            year = int(request.query_params.get("year", datetime.now().year))
            prev_year = year - 1

            # Account type categorizations
            NON_CASH_EXPENSES = {"Depreciation", "Amortization"}
            CURRENT_ASSET_TYPES = {"Current Asset"}
            CURRENT_LIABILITY_TYPES = {"Current Liability"}
            REVENUE_TYPES = {"Income", "Revenues", "Other Income"}
            EXPENSE_TYPES = {"Expense", "Expenses", "Cost of sales"}

            accounts = ChartofAccount.objects.filter(
                is_postable=True, frozen=False
            ).only("id", "account_type", "account_name", "cash_account", "bank_debt")

            # Initialize account ID groups
            starting_cash_ids = set()
            capex_ids = set()
            share_loan_ids = set()
            bank_loan_ids = set()
            dividend_ids = set()

            revenue_ids, expense_ids = set(), set()
            asset_ids, liability_ids = set(), set()
            non_cash_ids = set()

            # Categorize accounts
            for acc in accounts:
                name_lower = acc.account_name.lower()
                acc_type = acc.account_type

                if acc_type in {"Asset", "Assets"} and acc.cash_account:
                    starting_cash_ids.add(acc.id)
                elif acc_type in REVENUE_TYPES:
                    revenue_ids.add(acc.id)
                elif acc_type in EXPENSE_TYPES:
                    expense_ids.add(acc.id)
                elif (
                    acc_type in {"Asset", "Assets", "Non-current Asset"}
                    and not acc.cash_account
                ):
                    capex_ids.add(acc.id)
                elif acc_type == "Equity" and (
                    "share" in name_lower or "loan" in name_lower
                ):
                    share_loan_ids.add(acc.id)
                elif acc.bank_debt:
                    bank_loan_ids.add(acc.id)
                elif acc_type == "Equity" and "dividend" in name_lower:
                    dividend_ids.add(acc.id)

                if acc_type in CURRENT_ASSET_TYPES:
                    asset_ids.add(acc.id)
                elif acc_type in CURRENT_LIABILITY_TYPES:
                    liability_ids.add(acc.id)

                if any(term.lower() in name_lower for term in NON_CASH_EXPENSES):
                    non_cash_ids.add(acc.id)

            all_ids = (
                starting_cash_ids
                | revenue_ids
                | expense_ids
                | capex_ids
                | share_loan_ids
                | bank_loan_ids
                | dividend_ids
                | asset_ids
                | liability_ids
                | non_cash_ids
            )

            grouped = defaultdict(float)
            yearly_entries = defaultdict(list)
            closing_balances = defaultdict(lambda: defaultdict(float))

            # Step 1: Starting cash (all entries up to end of previous year)
            cash_entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=starting_cash_ids,
                    transaction_date__lte=datetime(prev_year, 12, 31),
                )
                .exclude(memo__icontains="FOR CLOSING")
                .values(
                    "chart_of_account_id",
                    "reporting_debit_amount",
                    "reporting_credit_amount",
                )
            )
            for e in cash_entries:
                debit = float(e["reporting_debit_amount"] or 0) * conversion_rate
                credit = float(e["reporting_credit_amount"] or 0) * conversion_rate
                grouped["starting_cash"] += debit - credit

            # Step 2: Entries in the current year
            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=all_ids,
                    transaction_date__year=year,
                )
                .exclude(memo__icontains="FOR CLOSING")
                .values(
                    "chart_of_account_id",
                    "transaction_date",
                    "reporting_debit_amount",
                    "reporting_credit_amount",
                )
            )

            for e in entries:
                acc_id = e["chart_of_account_id"]
                date = e["transaction_date"]
                debit = float(e["reporting_debit_amount"] or 0) * conversion_rate
                credit = float(e["reporting_credit_amount"] or 0) * conversion_rate
                net = debit - credit
                yearly_entries[year].append((acc_id, debit, credit))

                if acc_id in capex_ids:
                    grouped["capex"] += net
                elif acc_id in share_loan_ids:
                    grouped["share_loans"] += net
                elif acc_id in bank_loan_ids:
                    grouped["bank_loans"] += net
                elif acc_id in dividend_ids:
                    grouped["dividends"] += -abs(net)

                # Closing balances
                if acc_id in asset_ids:
                    balance = debit - credit
                elif acc_id in liability_ids:
                    balance = credit - debit
                else:
                    balance = debit - credit

                for y in [prev_year, year]:
                    if date <= datetime(y, 12, 31).date():
                        closing_balances[y][acc_id] += balance

            # Step 3: Compute OCF
            revenue = expense = non_cash = 0
            for acc_id, debit, credit in yearly_entries.get(year, []):
                if acc_id in revenue_ids:
                    revenue += credit - debit
                elif acc_id in expense_ids:
                    expense += debit - credit
                elif acc_id in non_cash_ids:
                    non_cash += debit - credit

            net_income = revenue - expense
            delta_assets = sum(
                closing_balances[year].get(a, 0) - closing_balances[prev_year].get(a, 0)
                for a in asset_ids
            )
            delta_liabilities = sum(
                closing_balances[year].get(l, 0) - closing_balances[prev_year].get(l, 0)
                for l in liability_ids
            )
            ocf = net_income + non_cash - delta_assets + delta_liabilities
            grouped["ocf"] = ocf

            # Step 4: Build waterfall chart
            start_cash = grouped["starting_cash"]
            flows = [
                {"name": str(prev_year), "amount": round(start_cash, 2), "remaining": 0}
            ]

            steps = {
                "OCF": grouped["ocf"],
                "CAPEX": -grouped["capex"],
                "Share<br/>Loans": grouped["share_loans"],
                "Bank<br/>Loan": grouped["bank_loans"],
                "Dividends": grouped["dividends"],
            }

            for name, value in steps.items():
                if round(value, 2) != 0:
                    flows.append({"name": name, "amount": round(value, 2)})

            running_total = start_cash
            for step in flows[1:]:
                step["remaining"] = round(running_total, 2)
                running_total += step["amount"]

            flows.append(
                {
                    "name": str(year),
                    "amount": round(running_total, 2),
                    "remaining": 0,
                }
            )

            data = {"chart_data": flows, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="ocf/chart")
    def ocf_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Refined account categorization
            REVENUE_TYPES = {"Income", "Revenues", "Other Income"}
            EXPENSE_TYPES = {"Expense", "Expenses", "Cost of sales"}
            NON_CASH_EXPENSES = {"Depreciation", "Amortization"}
            CURRENT_ASSET_TYPES = {"Current Asset"}
            CURRENT_LIABILITY_TYPES = {"Current Liability"}

            accounts = ChartofAccount.objects.filter(
                is_postable=True, frozen=False
            ).only("id", "account_type", "account_name")

            revenue_ids, expense_ids, asset_ids, liability_ids, non_cash_ids = (
                set(),
                set(),
                set(),
                set(),
                set(),
            )

            for acc in accounts:
                acc_type = acc.account_type
                name_lower = acc.account_name.lower()
                if acc_type in REVENUE_TYPES:
                    revenue_ids.add(acc.id)
                elif acc_type in EXPENSE_TYPES:
                    expense_ids.add(acc.id)
                elif acc_type in CURRENT_ASSET_TYPES:
                    asset_ids.add(acc.id)
                elif acc_type in CURRENT_LIABILITY_TYPES:
                    liability_ids.add(acc.id)
                if any(term.lower() in name_lower for term in NON_CASH_EXPENSES):
                    non_cash_ids.add(acc.id)

            all_ids = (
                revenue_ids | expense_ids | asset_ids | liability_ids | non_cash_ids
            )

            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=all_ids,
                    transaction_date__range=[
                        datetime(start_year - 1, 1, 1),
                        datetime(end_year, 12, 31),
                    ],
                )
                .exclude(memo__icontains="FOR CLOSING")
                .values(
                    "chart_of_account_id",
                    "transaction_date",
                    "reporting_debit_amount",
                    "reporting_credit_amount",
                )
            )

            yearly_entries = defaultdict(list)
            closing_balances = defaultdict(lambda: defaultdict(float))

            for e in entries:
                acc_id = e["chart_of_account_id"]
                date = e["transaction_date"]
                debit = float(e["reporting_debit_amount"] or 0) * conversion_rate
                credit = float(e["reporting_credit_amount"] or 0) * conversion_rate
                year_key = date.year

                yearly_entries[year_key].append((acc_id, debit, credit))

                if acc_id in asset_ids:
                    balance = debit - credit
                elif acc_id in liability_ids:
                    balance = credit - debit
                else:
                    balance = debit - credit

                for y in range(start_year - 1, end_year + 1):
                    if date <= datetime(y, 12, 31).date():
                        closing_balances[y][acc_id] += balance

            results = []

            for y in range(start_year, end_year + 1):
                revenue = expense = non_cash = 0

                for acc_id, debit, credit in yearly_entries.get(y, []):
                    if acc_id in revenue_ids:
                        revenue += credit - debit
                    elif acc_id in expense_ids:
                        expense += debit - credit
                    elif acc_id in non_cash_ids:
                        non_cash += debit - credit

                net_income = revenue - expense

                delta_assets = sum(
                    closing_balances[y].get(a, 0) - closing_balances[y - 1].get(a, 0)
                    for a in asset_ids
                )
                delta_liabilities = sum(
                    closing_balances[y].get(l, 0) - closing_balances[y - 1].get(l, 0)
                    for l in liability_ids
                )

                ocf = net_income + non_cash - delta_assets + delta_liabilities
                results.append({"name": str(y), "value": round(ocf, 2)})

            data = {"chart_data": results, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="fcf/chart")
    def fcf_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Refined account type categories
            REVENUE_TYPES = {"Income", "Revenues", "Other Income"}
            EXPENSE_TYPES = {"Expense", "Expenses", "Cost of sales"}
            NON_CASH_EXPENSES = {"Depreciation", "Amortization"}
            CURRENT_ASSET_TYPES = {"Current Asset"}
            CURRENT_LIABILITY_TYPES = {"Current Liability"}
            CAPEX_TYPES = {"Non-current Asset"}

            # Get all relevant accounts
            accounts = ChartofAccount.objects.filter(
                is_postable=True,
                frozen=False,
            ).only("id", "account_type", "account_name")

            # Categorize accounts in memory
            revenue_ids, expense_ids, non_cash_ids = set(), set(), set()
            asset_ids, liability_ids, capex_ids = set(), set(), set()

            for acc in accounts:
                acc_type = acc.account_type
                name_lower = acc.account_name.lower()
                if acc_type in REVENUE_TYPES:
                    revenue_ids.add(acc.id)
                elif acc_type in EXPENSE_TYPES:
                    expense_ids.add(acc.id)
                elif acc_type in CURRENT_ASSET_TYPES:
                    asset_ids.add(acc.id)
                elif acc_type in CURRENT_LIABILITY_TYPES:
                    liability_ids.add(acc.id)
                elif acc_type in CAPEX_TYPES:
                    capex_ids.add(acc.id)

                if any(nc.lower() in name_lower for nc in NON_CASH_EXPENSES):
                    non_cash_ids.add(acc.id)

            all_account_ids = (
                revenue_ids
                | expense_ids
                | capex_ids
                | asset_ids
                | liability_ids
                | non_cash_ids
            )

            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=all_account_ids,
                    transaction_date__range=[
                        datetime(start_year - 1, 1, 1),
                        datetime(end_year, 12, 31),
                    ],
                )
                .exclude(memo__icontains="FOR CLOSING")
                .values(
                    "chart_of_account_id",
                    "transaction_date",
                    "reporting_debit_amount",
                    "reporting_credit_amount",
                )
            )

            yearly_entries = defaultdict(list)
            cumulative_balances = defaultdict(lambda: defaultdict(float))

            for entry in entries:
                acc_id = entry["chart_of_account_id"]
                date = entry["transaction_date"]
                debit = float(entry["reporting_debit_amount"] or 0) * conversion_rate
                credit = float(entry["reporting_credit_amount"] or 0) * conversion_rate

                if acc_id in asset_ids or acc_id in capex_ids:
                    net_amount = debit - credit
                elif acc_id in liability_ids:
                    net_amount = credit - debit
                else:
                    net_amount = debit - credit

                year_key = date.year
                yearly_entries[year_key].append((acc_id, debit, credit))

                for y in range(start_year - 1, end_year + 1):
                    if date <= datetime(y, 12, 31).date():
                        cumulative_balances[y][acc_id] += net_amount

            fcf_by_year = {}

            for y in range(start_year - 1, end_year + 1):
                revenue = expense = non_cash = capex = 0

                for acc_id, debit, credit in yearly_entries.get(y, []):
                    if acc_id in revenue_ids:
                        revenue += credit - debit
                    elif acc_id in expense_ids:
                        expense += debit - credit
                    elif acc_id in non_cash_ids:
                        non_cash += debit - credit
                    elif acc_id in capex_ids:
                        capex += debit - credit

                net_income = revenue - expense

                delta_assets = sum(
                    cumulative_balances[y].get(acc, 0)
                    - cumulative_balances[y - 1].get(acc, 0)
                    for acc in asset_ids
                )
                delta_liabilities = sum(
                    cumulative_balances[y].get(acc, 0)
                    - cumulative_balances[y - 1].get(acc, 0)
                    for acc in liability_ids
                )

                ocf = net_income + non_cash - delta_assets + delta_liabilities
                fcf = ocf - capex
                fcf_by_year[y] = fcf

            response = []
            for y in range(start_year, end_year + 1):
                current = fcf_by_year.get(y, 0)
                previous = fcf_by_year.get(y - 1, 0)
                percent = (
                    ((current - previous) / abs(previous) * 100) if previous else 0.0
                )

                response.append(
                    {
                        "name": str(y),
                        "amount": round(current, 2),
                        "percentage": round(percent, 1),
                    }
                )

            data = {"chart_data": response, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )


class OperationsAndHRViewSet(ModelViewSet):
    @action(detail=False, methods=["get"], url_path="overview")
    def overview(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            chart_years = list(range(start_year, end_year + 1))

            # Get the latest batch number
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset for revenue using FinancialTransactionSnapshot
            revenue_queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="revenue",
                year__range=[start_year, end_year],
            )

            # Apply segment filter if provided
            if segments:
                revenue_queryset = revenue_queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                revenue_queryset = revenue_queryset.filter(clinic__code__in=clinics)

            # Get revenue data from FinancialTransactionSnapshot
            revenue_entries = revenue_queryset.values("year").annotate(
                total_amount=Sum("amount")
            )

            # Process revenue data
            revenue_by_year = {}
            for entry in revenue_entries:
                y = entry["year"]
                revenue_by_year[y] = float(entry["total_amount"]) * conversion_rate

            def get_revenue(y):
                return revenue_by_year.get(y, 0)

            # Pre-compute historical data for all years using optimized approach
            historical_data = {}

            VOLUNTARY_REASONS = {
                "Back to School",
                "Better Benefits",
                "Career Progression",
                "End of Contract",
                "Family Reasons",
                "Fellow Colleagues",
                "Further Education",
                "Health Reasons",
                "Job Mismatch",
                "Remunerations",
                "Resigned",
                "Supervisor / Management",
            }

            INVOLUNTARY_REASONS = {
                "Company Direction",
                "Dismissed",
                "Not for Rehire",
                "Terminated",
            }

            def safe_div(numerator, denominator):
                return float(numerator) / denominator if denominator else 0

            def pct_change(current, previous):
                if not previous:
                    return 100 if current else 0
                return round((current - previous) / previous * 100, 1)

            # OPTIMIZED: Single query approach to avoid N+1 problem
            # Fetch ALL relevant employees once
            # Note: We'll filter by segment/clinic after getting the data since clinic names
            # in Employee model might not exactly match Clinic model names
            all_employees = Employee.objects.filter(
                Q(joined_date__year__lte=end_year)
                & (
                    Q(resignation_date__isnull=True)
                    | Q(resignation_date__year__gte=start_year)
                )
            ).values(
                "joined_date",
                "resignation_date",
                "resignation_reason",
                "category",
                "department",
                "clinic_name",
            )

            # Build clinic segment mapping for filtering (using existing logic)
            clinic_segment_map = {}
            valid_segments = set()
            valid_clinic_codes = set()

            if segments or clinics:
                # Get clinic name → segment map (same as doctors_chart)
                clinic_qs = Clinic.objects.exclude(name="Ceased Clinic / Codes").values(
                    "name", "segment", "code"
                )
                clinic_segment_map = {
                    normalize_clinic_name(c["name"]): c["segment"] or "Others"
                    for c in clinic_qs
                }

                # Build sets of valid segments and clinic codes
                if segments:
                    valid_segments.update(segments)
                if clinics:
                    valid_clinic_codes.update(clinics)
                    # Also get segments for these clinic codes
                    for clinic in clinic_qs:
                        if clinic["code"] in clinics:
                            valid_segments.add(clinic["segment"] or "Others")

            # Fetch ALL resignations in the period once
            # Note: We'll filter by segment/clinic after getting the data
            all_resignations = Employee.objects.filter(
                resignation_date__year__gte=start_year,
                resignation_date__year__lte=end_year,
            ).values("resignation_date", "resignation_reason", "clinic_name")

            # Pre-compute resignation data for all years
            resignations_by_year = defaultdict(list)

            # Group resignations by year
            for resignation in all_resignations:
                # Apply segment/clinic filter if specified
                if segments or clinics:
                    resign_clinic_name = resignation["clinic_name"] or ""
                    # Use the same logic as doctors_chart to match clinic to segment
                    employee_segment = match_clinic_segment(
                        resign_clinic_name, clinic_segment_map
                    )

                    # Check if this employee's segment/clinic matches the filter
                    segment_matches = not segments or employee_segment in valid_segments

                    # For clinic filter, check if employee's clinic matches any specified clinic codes
                    clinic_matches = True
                    if clinics:
                        clinic_matches = False
                        normalized_clinic_name = normalize_clinic_name(
                            resign_clinic_name
                        )
                        for clinic_code in clinics:
                            try:
                                clinic_obj = Clinic.objects.get(code=clinic_code)
                                if (
                                    normalize_clinic_name(clinic_obj.name)
                                    == normalized_clinic_name
                                ):
                                    clinic_matches = True
                                    break
                            except Clinic.DoesNotExist:
                                continue

                    if not (segment_matches and clinic_matches):
                        continue

                resign_year = resignation["resignation_date"].year
                resignations_by_year[resign_year].append(
                    resignation["resignation_reason"]
                )

            # Calculate metrics for each year using pre-fetched data
            for calc_year in chart_years:
                # Filter employees active in this year from pre-fetched data
                active_employees = []
                for emp in all_employees:
                    # Check if employee was active in this year
                    if emp["joined_date"].year <= calc_year and (
                        not emp["resignation_date"]
                        or emp["resignation_date"].year > calc_year
                    ):
                        # Apply segment/clinic filter if specified
                        if segments or clinics:
                            emp_clinic_name = emp["clinic_name"] or ""
                            # Use the same logic as doctors_chart to match clinic to segment
                            employee_segment = match_clinic_segment(
                                emp_clinic_name, clinic_segment_map
                            )

                            # Check if this employee's segment/clinic matches the filter
                            segment_matches = (
                                not segments or employee_segment in valid_segments
                            )

                            # For clinic filter, check if employee's clinic matches any specified clinic codes
                            clinic_matches = True
                            if clinics:
                                clinic_matches = False
                                normalized_clinic_name = normalize_clinic_name(
                                    emp_clinic_name
                                )
                                for clinic_code in clinics:
                                    try:
                                        clinic_obj = Clinic.objects.get(
                                            code=clinic_code
                                        )
                                        if (
                                            normalize_clinic_name(clinic_obj.name)
                                            == normalized_clinic_name
                                        ):
                                            clinic_matches = True
                                            break
                                    except Clinic.DoesNotExist:
                                        continue

                            if not (segment_matches and clinic_matches):
                                continue

                        active_employees.append(emp)

                # Calculate employee metrics
                total_employees = len(active_employees)
                total_fte = sum(
                    FTE_WEIGHTS.get(emp["category"], 0.0) for emp in active_employees
                )
                doctors = sum(
                    1 for emp in active_employees if emp["department"] == "Doctor"
                )

                # Get revenue for this year
                revenue = get_revenue(calc_year)

                # Calculate per-employee metrics
                rev_per_fte = safe_div(revenue, total_fte)
                rev_per_doc = safe_div(revenue, doctors)

                # Calculate attrition rates using pre-grouped data
                year_resignations = resignations_by_year.get(calc_year, [])
                voluntary_count = sum(
                    1 for reason in year_resignations if reason in VOLUNTARY_REASONS
                )
                involuntary_count = sum(
                    1 for reason in year_resignations if reason in INVOLUNTARY_REASONS
                )

                pos_rate = (
                    (voluntary_count / total_employees * 100) if total_employees else 0
                )
                neg_rate = (
                    (involuntary_count / total_employees * 100)
                    if total_employees
                    else 0
                )

                historical_data[calc_year] = {
                    "revenue_per_fte": round(rev_per_fte, 0),
                    "revenue_per_doctor": round(rev_per_doc, 0),
                    "employees": total_employees,
                    "positive_attrition_rate": round(pos_rate, 1),
                    "negative_attrition_rate": round(neg_rate, 1),
                }

            # Current & Previous year data
            curr = historical_data.get(
                end_year,
                {
                    "revenue_per_fte": 0,
                    "revenue_per_doctor": 0,
                    "employees": 0,
                    "doctors": 0,
                    "clinics": 0,
                    "revenue": 0,
                    "positive_attrition_rate": 0,
                    "negative_attrition_rate": 0,
                },
            )
            prev = historical_data.get(
                end_year - 1,
                {
                    "revenue_per_fte": 0,
                    "revenue_per_doctor": 0,
                    "employees": 0,
                    "positive_attrition_rate": 0,
                    "negative_attrition_rate": 0,
                },
            )

            overview_data = {
                "revenue_per_fte": {
                    "value": curr["revenue_per_fte"],
                    "percentage": pct_change(
                        curr["revenue_per_fte"], prev["revenue_per_fte"]
                    ),
                    "chart_data": [
                        {"name": str(y), "value": historical_data[y]["revenue_per_fte"]}
                        for y in chart_years
                    ],
                },
                "revenue_per_doctor": {
                    "value": curr["revenue_per_doctor"],
                    "percentage": pct_change(
                        curr["revenue_per_doctor"], prev["revenue_per_doctor"]
                    ),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": historical_data[y]["revenue_per_doctor"],
                        }
                        for y in chart_years
                    ],
                },
                "employees": {
                    "value": curr["employees"],
                    "percentage": pct_change(curr["employees"], prev["employees"]),
                    "chart_data": [
                        {"name": str(y), "value": historical_data[y]["employees"]}
                        for y in chart_years
                    ],
                },
                "positive_attrition_rate": {
                    "value": curr["positive_attrition_rate"],
                    "percentage": pct_change(
                        curr["positive_attrition_rate"], prev["positive_attrition_rate"]
                    ),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": historical_data[y]["positive_attrition_rate"],
                        }
                        for y in chart_years
                    ],
                },
                "negative_attrition_rate": {
                    "value": curr["negative_attrition_rate"],
                    "percentage": pct_change(
                        curr["negative_attrition_rate"], prev["negative_attrition_rate"]
                    ),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": historical_data[y]["negative_attrition_rate"],
                        }
                        for y in chart_years
                    ],
                },
            }

            return custom_success_response(overview_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="segments/chart")
    def segments_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)
            year = int(request.query_params.get("year", datetime.now().year))

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset for revenue
            revenue_queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="revenue",
                year=year,
                amount__gt=0,  # TODO: remove this later if not needed
            ).exclude(clinic__segment="Corporate")

            # Apply segment filter if provided
            if segments:
                revenue_queryset = revenue_queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                revenue_queryset = revenue_queryset.filter(clinic__code__in=clinics)

            # --- Step 1: Revenue by segment ---
            revenue_entries = (
                revenue_queryset.select_related("clinic")
                .values("clinic__segment")
                .annotate(total_revenue=models.Sum("amount"))
                .order_by("-total_revenue")
            )

            # Build base queryset for clinics count
            clinics_queryset = Clinic.objects.exclude(
                name="Ceased Clinic / Codes"
            ).exclude(segment="Corporate")

            # Apply segment filter if provided
            if segments:
                clinics_queryset = clinics_queryset.filter(segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                clinics_queryset = clinics_queryset.filter(code__in=clinics)

            # --- Step 2: Clinics per segment ---
            clinics_count = clinics_queryset.values("segment").annotate(
                clinic_count=models.Count("id", distinct=True)
            )

            clinics_map = {c["segment"]: c["clinic_count"] for c in clinics_count}

            # --- Step 3: Headcounts per segment ---
            employees = Employee.objects.filter(joined_date__year__lte=year).values(
                "clinic_name", "joined_date", "resignation_date"
            )
            clinic_qs = Clinic.objects.exclude(name="Ceased Clinic / Codes").values(
                "name", "segment"
            )
            normalized_to_segment = {
                normalize_clinic_name(c["name"]): c["segment"] or "Others"
                for c in clinic_qs
            }
            headcounts_map = defaultdict(int)
            for emp in employees:
                seg = match_clinic_segment(emp["clinic_name"], normalized_to_segment)
                if seg == "Corporate":
                    continue
                joined_year = emp["joined_date"].year if emp["joined_date"] else None
                resigned_year = (
                    emp["resignation_date"].year if emp["resignation_date"] else None
                )
                if (
                    joined_year
                    and joined_year <= year
                    and (not resigned_year or resigned_year > year)
                ):
                    headcounts_map[seg] += 1

            # --- Step 4: Doctors per segment ---
            doctor_qs = Employee.objects.filter(
                department="Doctor", joined_date__year__lte=year
            ).values("clinic_name", "joined_date", "resignation_date")
            doctors_map = defaultdict(int)
            for emp in doctor_qs:
                seg = match_clinic_segment(emp["clinic_name"], normalized_to_segment)
                if seg == "Corporate":
                    continue
                joined_year = emp["joined_date"].year
                resigned_year = (
                    emp["resignation_date"].year if emp["resignation_date"] else None
                )
                if joined_year <= year and (not resigned_year or resigned_year > year):
                    doctors_map[seg] += 1

            fte_employees = Employee.objects.filter(joined_date__year__lte=year).only(
                "department",
                "category",
                "joined_date",
                "resignation_date",
                "clinic_name",
            )
            fte_map = defaultdict(float)
            for emp in fte_employees:
                seg = match_clinic_segment(emp.clinic_name, normalized_to_segment)
                if seg == "Corporate":
                    continue
                fte_val = FTE_WEIGHTS.get((emp.category or "").strip(), 0.0)
                if fte_val == 0:
                    continue
                joined_year = emp.joined_date.year if emp.joined_date else None
                resigned_year = (
                    emp.resignation_date.year if emp.resignation_date else None
                )
                if (
                    joined_year
                    and joined_year <= year
                    and (not resigned_year or resigned_year > year)
                ):
                    fte_map[seg] += fte_val

            # --- Step 6: Merge all into final result ---
            result = []
            for entry in revenue_entries:
                seg = entry["clinic__segment"] or "Others"
                result.append(
                    {
                        "name": seg,
                        "revenue": float(entry["total_revenue"]) * conversion_rate,
                        "head_counts": headcounts_map.get(seg, 0),
                        "clinics": clinics_map.get(seg, 0),
                        "doctors": doctors_map.get(seg, 0),
                        "ftes": round(fte_map.get(seg, 0), 2),
                    }
                )

            data = {"chart_data": result, "commentary": ""}
            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="clinics/chart")
    def clinics_chart(self, request):
        try:
            segments = request.query_params.getlist("segment", None)
            clinics_filter = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Build base queryset for clinics
            clinics_queryset = Clinic.objects.exclude(name="Ceased Clinic / Codes")

            # Apply segment filter if provided
            if segments:
                clinics_queryset = clinics_queryset.filter(segment__in=segments)

            # Apply clinic filter if provided
            if clinics_filter:
                clinics_queryset = clinics_queryset.filter(code__in=clinics_filter)

            # Build clinic_code → segment map
            clinics = clinics_queryset.values("code", "segment")
            clinic_segment_map = {c["code"]: c["segment"] or "Others" for c in clinics}
            valid_clinic_codes = set(clinic_segment_map.keys())

            # Get relevant revenue accounts
            coa_qs = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"],
                clinic_code__in=valid_clinic_codes,
            ).values("id", "clinic_code")

            coa_id_to_clinic = {c["id"]: c["clinic_code"] for c in coa_qs}
            coa_ids = list(coa_id_to_clinic.keys())

            # Query all relevant journal entries in one go
            start_date = datetime(start_year, 1, 1)
            end_date = datetime(end_year, 12, 31)

            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=coa_ids,
                    transaction_date__range=(start_date, end_date),
                )
                .exclude(memo__icontains="FOR CLOSING")
                .annotate(year=ExtractYear("transaction_date"))
                .values("year", "chart_of_account_id")
                .distinct()
            )

            # Step 1: collect distinct (year, clinic_code)
            year_clinic_pairs = set()
            for e in entries:
                y = e["year"]
                coa_id = e["chart_of_account_id"]
                clinic_code = coa_id_to_clinic.get(coa_id)
                if clinic_code:
                    year_clinic_pairs.add((y, clinic_code))

            # Step 2: aggregate by segment
            chart_data = defaultdict(lambda: defaultdict(int))
            all_segments = set()

            for y, clinic_code in year_clinic_pairs:
                segment = clinic_segment_map.get(clinic_code, "Others")
                if segment == "Corporate":
                    continue
                chart_data[y][segment] += 1
                chart_data[y]["total"] += 1
                all_segments.add(segment)

            # Sort segments
            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            # Format result
            result = []
            for y in range(start_year, end_year + 1):
                row = {"name": str(y)}
                for seg in final_segments:
                    row[seg] = chart_data[y].get(seg, 0)
                row["total"] = chart_data[y].get("total", 0)
                result.append(row)

            data = {"chart_data": result, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="doctors/chart")
    def doctors_chart(self, request):
        try:
            segments = request.query_params.getlist("segment", None)
            clinics_filter = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Build base queryset for clinics
            clinic_queryset = Clinic.objects.exclude(name="Ceased Clinic / Codes")

            # Apply segment filter if provided
            if segments:
                clinic_queryset = clinic_queryset.filter(segment__in=segments)

            # Apply clinic filter if provided
            if clinics_filter:
                clinic_queryset = clinic_queryset.filter(code__in=clinics_filter)

            # Prepare clinic name → segment map (normalized)
            clinic_qs = clinic_queryset.values("name", "segment")
            normalized_segment_map = {
                normalize_clinic_name(c["name"]): c["segment"] or "Others"
                for c in clinic_qs
            }

            # Filter doctors who joined before or during the period
            # Note: We'll filter by segment after getting the data since clinic names
            # in Employee model might not exactly match Clinic model names
            employees = Employee.objects.filter(
                department="Doctor", joined_date__year__lte=end_year
            ).values("clinic_name", "joined_date", "resignation_date")

            chart_data = defaultdict(lambda: defaultdict(int))
            all_segments = set()

            for emp in employees:
                clinic_name = emp["clinic_name"]
                segment = match_clinic_segment(clinic_name, normalized_segment_map)

                # Apply segment filter if provided
                if segments and segment not in segments:
                    continue

                # Apply clinic filter if provided
                if clinics_filter:
                    # Check if this employee's clinic matches any of the specified clinic codes
                    normalized_clinic_name = normalize_clinic_name(clinic_name)
                    employee_matches_clinic = False
                    for clinic_code in clinics_filter:
                        # Get the clinic name for this code and check if it matches
                        try:
                            clinic_obj = Clinic.objects.get(code=clinic_code)
                            if (
                                normalize_clinic_name(clinic_obj.name)
                                == normalized_clinic_name
                            ):
                                employee_matches_clinic = True
                                break
                        except Clinic.DoesNotExist:
                            continue

                    if not employee_matches_clinic:
                        continue

                all_segments.add(segment)

                joined_year = emp["joined_date"].year
                resigned_year = (
                    emp["resignation_date"].year if emp["resignation_date"] else None
                )

                for y in range(start_year, end_year + 1):
                    if joined_year <= y and (
                        resigned_year is None or resigned_year > y
                    ):
                        chart_data[y][segment] += 1
                        chart_data[y]["total"] += 1

            # Sort segments, put "Others" at the end
            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            result = []
            for y in range(start_year, end_year + 1):
                row = {"name": str(y)}
                for seg in final_segments:
                    row[seg] = chart_data[y].get(seg, 0)
                row["total"] = chart_data[y].get("total", 0)
                result.append(row)

            data = {"chart_data": result, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="clinics-revenue-analysis/chart")
    def clinics_revenue_analysis_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)
            year = int(request.query_params.get("year", datetime.now().year))

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset
            queryset = (
                FinancialTransactionSnapshot.objects.filter(
                    batch_number=latest_batch_number,
                    financial_statement_type="revenue",
                    year=year,
                    amount__gt=0,  # TODO: remove this later if not needed
                )
                .exclude(clinic__segment="Corporate")
                .exclude(clinic__name="Ceased Clinic / Codes")
            )

            # Apply segment filter if provided
            if segments:
                queryset = queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                queryset = queryset.filter(clinic__code__in=clinics)

            # --- Revenue by clinic (not segment) ---
            revenue_entries = (
                queryset.select_related("clinic")
                .values("clinic__name")
                .annotate(total_revenue=models.Sum("amount"))
                .order_by("clinic__name")
            )

            # --- Final result: per clinic ---
            result = []
            for entry in revenue_entries:
                clinic_name = entry["clinic__name"] or "Unknown Clinic"
                revenue = entry["total_revenue"] or Decimal("0.0")
                revenue_float = float(revenue) * conversion_rate
                result.append(
                    {
                        "name": clinic_name,
                        "amount": revenue_float,
                        "target": round(
                            revenue_float * 1.1, 2
                        ),  # TODO: Update target later
                    }
                )

            data = {"chart_data": result, "commentary": ""}
            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="fte/chart")
    def fte_chart(self, request):
        try:
            segments = request.query_params.getlist("segment", None)
            clinics_filter = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # Department to FTE category mapping
            CATEGORY_MAP = {
                "Corporate": [
                    "HR",
                    "Finance",
                    "Information Technology",
                    "Marketing",
                    "Admin",
                    "Management",
                    "Operations",
                    "Sales",
                ],
                "PSA": ["Front Desk", "Call Centre", "Despatch"],
                "Clinic": [
                    "Clinical",
                    "Imaging",
                    "Optometry",
                    "Allied Health",
                    "Housekeeping",
                ],
                "Nurse": ["Nursing"],
                "Doctor": ["Doctor"],
            }

            def get_category(department: str) -> str:
                for category, departments in CATEGORY_MAP.items():
                    if department in departments:
                        return category
                return "Other"

            # Build base queryset for employees
            employees_queryset = Employee.objects.filter(
                joined_date__year__lte=end_year
            )

            # Apply segment filter if provided
            if segments:
                # Get clinic names for the specified segments
                clinic_names = Clinic.objects.filter(segment__in=segments).values_list(
                    "name", flat=True
                )
                employees_queryset = employees_queryset.filter(
                    clinic_name__in=clinic_names
                )

            # Apply clinic filter if provided
            if clinics_filter:
                # Get clinic names for the specified clinic codes
                clinic_names = Clinic.objects.filter(
                    code__in=clinics_filter
                ).values_list("name", flat=True)
                employees_queryset = employees_queryset.filter(
                    clinic_name__in=clinic_names
                )

            # Preload all relevant employees
            employees = employees_queryset.only(
                "department",
                "category",
                "joined_date",
                "resignation_date",
                "clinic_name",
            )

            chart_data = defaultdict(lambda: defaultdict(float))

            for emp in employees:
                department = (emp.department or "").strip()
                emp_type = (emp.category or "").strip()
                fte_value = FTE_WEIGHTS.get(emp_type, 0.0)
                if fte_value == 0:
                    continue  # skip unknown types

                category = get_category(department)

                joined_year = emp.joined_date.year if emp.joined_date else None
                resigned_year = (
                    emp.resignation_date.year if emp.resignation_date else None
                )

                for y in range(start_year, end_year + 1):
                    if (
                        joined_year
                        and joined_year <= y
                        and (not resigned_year or resigned_year > y)
                    ):
                        chart_data[y][category] += fte_value
                        chart_data[y]["total"] += fte_value

            # Final result formatting
            result = []
            all_categories = list(CATEGORY_MAP.keys()) + ["Other"]

            for y in range(start_year, end_year + 1):
                row = {"name": str(y)}
                for cat in all_categories:
                    row[cat] = round(chart_data[y].get(cat, 0.0), 2)
                row["total"] = round(chart_data[y].get("total", 0.0), 2)
                result.append(row)

            data = {"chart_data": result, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e),
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["get"], url_path="fte-demographics/chart")
    def fte_demographics_chart(self, request):
        try:
            segments = request.query_params.getlist("segment", None)
            clinics_filter = request.query_params.getlist("clinic", None)

            year = int(request.query_params.get("year", datetime.now().year))
            snapshot_date = datetime(year, 12, 31).date()  # use Dec 31 of given year

            # Department to FTE category mapping
            CATEGORY_MAP = {
                "Corporate": [
                    "HR",
                    "Finance",
                    "Information Technology",
                    "Marketing",
                    "Admin",
                    "Management",
                    "Operations",
                    "Sales",
                ],
                "PSA": ["Front Desk", "Call Centre", "Despatch"],
                "Clinic": [
                    "Clinical",
                    "Imaging",
                    "Optometry",
                    "Allied Health",
                    "Housekeeping",
                ],
                "Nurse": ["Nursing"],
                "Doctor": ["Doctor"],
            }

            SERVICE_BUCKETS = [
                (0, 1, "0-1"),
                (2, 3, "2-3"),
                (4, 5, "4-5"),
                (6, 7, "6-7"),
                (8, 9, "8-9"),
                (10, 100, "10+"),  # assume max cap 100 yrs
            ]

            def get_category(department: str) -> str:
                for category, departments in CATEGORY_MAP.items():
                    if department in departments:
                        return category
                return "Other"

            def get_service_bucket(years: int) -> str:
                for start, end, label in SERVICE_BUCKETS:
                    if start <= years <= end:
                        return label
                return "10+"

            # Preload all relevant employees
            # Note: We'll filter by segment/clinic after getting the data since clinic names
            # in Employee model might not exactly match Clinic model names
            employees = Employee.objects.only(
                "department",
                "category",
                "joined_date",
                "resignation_date",
                "clinic_name",
            )

            # Build clinic segment mapping for filtering (using existing logic)
            clinic_segment_map = {}
            valid_segments = set()

            if segments or clinics_filter:
                # Get clinic name → segment map (same as doctors_chart)
                clinic_qs = Clinic.objects.exclude(name="Ceased Clinic / Codes").values(
                    "name", "segment", "code"
                )
                clinic_segment_map = {
                    normalize_clinic_name(c["name"]): c["segment"] or "Others"
                    for c in clinic_qs
                }

                # Build sets of valid segments and clinic codes
                if segments:
                    valid_segments.update(segments)
                if clinics_filter:
                    # Also get segments for these clinic codes
                    for clinic in clinic_qs:
                        if clinic["code"] in clinics_filter:
                            valid_segments.add(clinic["segment"] or "Others")

            chart_data = defaultdict(lambda: defaultdict(float))

            for emp in employees:
                # Apply segment/clinic filter if specified
                if segments or clinics_filter:
                    emp_clinic_name = emp.clinic_name or ""
                    # Use the same logic as doctors_chart to match clinic to segment
                    employee_segment = match_clinic_segment(
                        emp_clinic_name, clinic_segment_map
                    )

                    # Check if this employee's segment/clinic matches the filter
                    segment_matches = not segments or employee_segment in valid_segments

                    # For clinic filter, check if employee's clinic matches any specified clinic codes
                    clinic_matches = True
                    if clinics_filter:
                        clinic_matches = False
                        normalized_clinic_name = normalize_clinic_name(emp_clinic_name)
                        for clinic_code in clinics_filter:
                            try:
                                clinic_obj = Clinic.objects.get(code=clinic_code)
                                if (
                                    normalize_clinic_name(clinic_obj.name)
                                    == normalized_clinic_name
                                ):
                                    clinic_matches = True
                                    break
                            except Clinic.DoesNotExist:
                                continue

                    if not (segment_matches and clinic_matches):
                        continue

                department = (emp.department or "").strip()
                emp_type = (emp.category or "").strip()
                fte_value = FTE_WEIGHTS.get(emp_type, 0.0)
                if fte_value == 0:
                    continue  # skip unknown types

                category = get_category(department)

                if not emp.joined_date:
                    continue

                # normalize joined/resignation to date
                joined_date = (
                    emp.joined_date
                    if isinstance(emp.joined_date, date)
                    else emp.joined_date.date()
                )
                resignation_date = None
                if emp.resignation_date:
                    resignation_date = (
                        emp.resignation_date
                        if isinstance(emp.resignation_date, date)
                        else emp.resignation_date.date()
                    )

                # skip if joined after snapshot
                if joined_date > snapshot_date:
                    continue
                # skip if resigned on/before snapshot
                if resignation_date and resignation_date <= snapshot_date:
                    continue

                years_of_service = (snapshot_date - joined_date).days // 365
                bucket = get_service_bucket(years_of_service)

                chart_data[bucket][category] += fte_value
                chart_data[bucket]["total"] += fte_value

            # Final result formatting
            result = []
            all_categories = list(CATEGORY_MAP.keys())

            for _, _, bucket_label in SERVICE_BUCKETS:
                row = {"name": bucket_label}
                for cat in all_categories:
                    row[cat] = round(chart_data[bucket_label].get(cat, 0.0), 2)
                row["total"] = round(chart_data[bucket_label].get("total", 0.0), 2)
                result.append(row)

            data = {"chart_data": result, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e),
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["get"], url_path="headcount/chart")
    def headcount_chart(self, request):
        try:
            segments = request.query_params.getlist("segment", None)
            clinics_filter = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            emp_type = (
                request.query_params.get("type", "full-time").replace("-", " ").lower()
            )

            # Build base queryset for clinics
            clinic_queryset = Clinic.objects.exclude(name="Ceased Clinic / Codes")

            # Apply segment filter if provided
            if segments:
                clinic_queryset = clinic_queryset.filter(segment__in=segments)

            # Apply clinic filter if provided
            if clinics_filter:
                clinic_queryset = clinic_queryset.filter(code__in=clinics_filter)

            # Get clinic name → segment map
            clinic_qs = clinic_queryset.values("name", "segment")
            normalized_to_segment = {
                normalize_clinic_name(c["name"]): c["segment"] or "Others"
                for c in clinic_qs
            }

            # Determine category filter keyword
            category_filter = "Part Time" if "part" in emp_type else "Full Time"

            # Build base queryset for employees
            employees_queryset = Employee.objects.filter(
                category__icontains=category_filter,
                joined_date__year__lte=end_year,
            )

            # Apply segment filter if provided
            if segments:
                # Get clinic names for the specified segments
                clinic_names = Clinic.objects.filter(segment__in=segments).values_list(
                    "name", flat=True
                )
                employees_queryset = employees_queryset.filter(
                    clinic_name__in=clinic_names
                )

            # Apply clinic filter if provided
            if clinics_filter:
                # Get clinic names for the specified clinic codes
                clinic_names = Clinic.objects.filter(
                    code__in=clinics_filter
                ).values_list("name", flat=True)
                employees_queryset = employees_queryset.filter(
                    clinic_name__in=clinic_names
                )

            # Filter employees with matching type and joining before end year
            employees = employees_queryset.only(
                "clinic_name", "joined_date", "resignation_date"
            )

            chart_data = defaultdict(lambda: defaultdict(int))
            all_segments = set()

            for emp in employees:
                segment = match_clinic_segment(emp.clinic_name, normalized_to_segment)
                all_segments.add(segment)

                joined_year = emp.joined_date.year if emp.joined_date else None
                resigned_year = (
                    emp.resignation_date.year if emp.resignation_date else None
                )

                for y in range(start_year, end_year + 1):
                    if joined_year <= y and (not resigned_year or resigned_year > y):
                        chart_data[y][segment] += 1
                        chart_data[y]["total"] += 1

            # Segment ordering with 'Others' at the end
            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            # Build final result
            result = []
            for y in range(start_year, end_year + 1):
                row = {"name": str(y)}
                for segment in final_segments:
                    row[segment] = chart_data[y].get(segment, 0)
                row["total"] = chart_data[y].get("total", 0)
                result.append(row)

            data = {"chart_data": result, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="headcount-demographics/chart")
    def headcount_demographics_chart(self, request):
        try:
            segments = request.query_params.getlist("segment", None)
            clinics_filter = request.query_params.getlist("clinic", None)

            year = int(request.query_params.get("year", datetime.now().year))
            snapshot_date = datetime(year, 12, 31).date()  # use Dec 31 of given year

            # Department to HEADCOUNT category mapping
            CATEGORY_MAP = {
                "Corporate": [
                    "HR",
                    "Finance",
                    "Information Technology",
                    "Marketing",
                    "Admin",
                    "Management",
                    "Operations",
                    "Sales",
                ],
                "PSA": ["Front Desk", "Call Centre", "Despatch"],
                "Clinic": [
                    "Clinical",
                    "Imaging",
                    "Optometry",
                    "Allied Health",
                    "Housekeeping",
                ],
                "Nurse": ["Nursing"],
                "Doctor": ["Doctor"],
            }

            SERVICE_BUCKETS = [
                (0, 1, "0-1"),
                (2, 3, "2-3"),
                (4, 5, "4-5"),
                (6, 7, "6-7"),
                (8, 9, "8-9"),
                (10, 100, "10+"),  # assume max cap 100 yrs
            ]

            def get_category(department: str) -> str:
                for category, departments in CATEGORY_MAP.items():
                    if department in departments:
                        return category
                return "Other"

            def get_service_bucket(years: int) -> str:
                for start, end, label in SERVICE_BUCKETS:
                    if start <= years <= end:
                        return label
                return "10+"

            # Preload all relevant employees
            # Note: We'll filter by segment/clinic after getting the data since clinic names
            # in Employee model might not exactly match Clinic model names
            employees = Employee.objects.only(
                "department",
                "category",
                "joined_date",
                "resignation_date",
                "clinic_name",
            )

            # Build clinic segment mapping for filtering (using existing logic)
            clinic_segment_map = {}
            valid_segments = set()

            if segments or clinics_filter:
                # Get clinic name → segment map (same as doctors_chart)
                clinic_qs = Clinic.objects.exclude(name="Ceased Clinic / Codes").values(
                    "name", "segment", "code"
                )
                clinic_segment_map = {
                    normalize_clinic_name(c["name"]): c["segment"] or "Others"
                    for c in clinic_qs
                }

                # Build sets of valid segments and clinic codes
                if segments:
                    valid_segments.update(segments)
                if clinics_filter:
                    # Also get segments for these clinic codes
                    for clinic in clinic_qs:
                        if clinic["code"] in clinics_filter:
                            valid_segments.add(clinic["segment"] or "Others")

            chart_data = defaultdict(lambda: defaultdict(float))

            for emp in employees:
                # Apply segment/clinic filter if specified
                if segments or clinics_filter:
                    emp_clinic_name = emp.clinic_name or ""
                    # Use the same logic as doctors_chart to match clinic to segment
                    employee_segment = match_clinic_segment(
                        emp_clinic_name, clinic_segment_map
                    )

                    # Check if this employee's segment/clinic matches the filter
                    segment_matches = not segments or employee_segment in valid_segments

                    # For clinic filter, check if employee's clinic matches any specified clinic codes
                    clinic_matches = True
                    if clinics_filter:
                        clinic_matches = False
                        normalized_clinic_name = normalize_clinic_name(emp_clinic_name)
                        for clinic_code in clinics_filter:
                            try:
                                clinic_obj = Clinic.objects.get(code=clinic_code)
                                if (
                                    normalize_clinic_name(clinic_obj.name)
                                    == normalized_clinic_name
                                ):
                                    clinic_matches = True
                                    break
                            except Clinic.DoesNotExist:
                                continue

                    if not (segment_matches and clinic_matches):
                        continue

                department = (emp.department or "").strip()
                category = get_category(department)

                if not emp.joined_date:
                    continue

                # normalize joined/resignation to date
                joined_date = (
                    emp.joined_date
                    if isinstance(emp.joined_date, date)
                    else emp.joined_date.date()
                )
                resignation_date = None
                if emp.resignation_date:
                    resignation_date = (
                        emp.resignation_date
                        if isinstance(emp.resignation_date, date)
                        else emp.resignation_date.date()
                    )

                # skip if joined after snapshot
                if joined_date > snapshot_date:
                    continue
                # skip if resigned on/before snapshot
                if resignation_date and resignation_date <= snapshot_date:
                    continue

                years_of_service = (snapshot_date - joined_date).days // 365
                bucket = get_service_bucket(years_of_service)

                # Headcount (each employee = 1)
                chart_data[bucket][category] += 1
                chart_data[bucket]["total"] += 1

            # Final result formatting
            result = []
            all_categories = list(CATEGORY_MAP.keys())

            for _, _, bucket_label in SERVICE_BUCKETS:
                row = {"name": bucket_label}
                for cat in all_categories:
                    row[cat] = int(chart_data[bucket_label].get(cat, 0))
                row["total"] = int(chart_data[bucket_label].get("total", 0))
                result.append(row)

            data = {"chart_data": result, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e),
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["get"], url_path="org-chart")
    def org_chart(self, request):
        try:
            segments = request.query_params.getlist("segment", None)
            clinics_filter = request.query_params.getlist("clinic", None)

            # Fetch all active employees
            # Note: We'll filter by segment/clinic after getting the data since clinic names
            # in Employee model might not exactly match Clinic model names
            employees = Employee.objects.filter(resignation_date__isnull=True).only(
                "clinic_name", "department", "occupation", "code"
            )

            # Build clinic segment mapping for filtering (using existing logic)
            clinic_segment_map = {}
            valid_segments = set()

            if segments or clinics_filter:
                # Get clinic name → segment map (same as doctors_chart)
                clinic_qs = Clinic.objects.exclude(name="Ceased Clinic / Codes").values(
                    "name", "segment", "code"
                )
                clinic_segment_map = {
                    normalize_clinic_name(c["name"]): c["segment"] or "Others"
                    for c in clinic_qs
                }

                # Build sets of valid segments and clinic codes
                if segments:
                    valid_segments.update(segments)
                if clinics_filter:
                    # Also get segments for these clinic codes
                    for clinic in clinic_qs:
                        if clinic["code"] in clinics_filter:
                            valid_segments.add(clinic["segment"] or "Others")

            # clinic_name -> department_group -> occupation -> list of codes
            hierarchy = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))

            department_mapping = {
                "Doctor": "Doctor",
                "Clinical": "Clinical",
                "Front Desk": "Front Desk",
            }

            for emp in employees:
                # Apply segment/clinic filter if specified
                if segments or clinics_filter:
                    emp_clinic_name = emp.clinic_name or ""
                    # Use the same logic as doctors_chart to match clinic to segment
                    employee_segment = match_clinic_segment(
                        emp_clinic_name, clinic_segment_map
                    )

                    # Check if this employee's segment/clinic matches the filter
                    segment_matches = not segments or employee_segment in valid_segments

                    # For clinic filter, check if employee's clinic matches any specified clinic codes
                    clinic_matches = True
                    if clinics_filter:
                        clinic_matches = False
                        normalized_clinic_name = normalize_clinic_name(emp_clinic_name)
                        for clinic_code in clinics_filter:
                            try:
                                clinic_obj = Clinic.objects.get(code=clinic_code)
                                if (
                                    normalize_clinic_name(clinic_obj.name)
                                    == normalized_clinic_name
                                ):
                                    clinic_matches = True
                                    break
                            except Clinic.DoesNotExist:
                                continue

                    if not (segment_matches and clinic_matches):
                        continue

                clinic = emp.clinic_name or "Unknown"
                department = emp.department
                occupation = emp.occupation or "Unknown"
                code = emp.code or "N/A"

                if department in department_mapping:
                    group = department_mapping[department]
                    hierarchy[clinic][group][occupation].append(code)

            def build_tree(name, children_dict):
                """Recursively builds the tree structure for org chart"""
                children = []
                for key, val in children_dict.items():
                    if isinstance(val, dict):
                        children.append(build_tree(key, val))
                    elif isinstance(val, list):
                        children.append(
                            {"name": key, "children": [{"name": code} for code in val]}
                        )
                return {"name": name, "children": children}

            # Assemble the final org chart
            org_chart = {
                "name": "SMG",
                "children": [
                    build_tree(clinic, dept_tree)
                    for clinic, dept_tree in sorted(hierarchy.items())
                ],
            }

            return custom_success_response(org_chart)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="staff-growth/chart")
    def staff_growth_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            today = date.today()

            CATEGORY_MAP = {
                "Corporate": [
                    "HR",
                    "Finance",
                    "Information Technology",
                    "Marketing",
                    "Admin",
                    "Management",
                    "Operations",
                    "Sales",
                ],
                "PSA": ["Front Desk", "Call Centre", "Despatch"],
                "Clinic": [
                    "Clinical",
                    "Imaging",
                    "Optometry",
                    "Allied Health",
                    "Housekeeping",
                ],
                "Nurse": ["Nursing"],
                "Doctor": ["Doctor"],
            }

            def get_category(department: str) -> str:
                for category, departments in CATEGORY_MAP.items():
                    if department in departments:
                        return category
                return "Other"

            # Fetch all employees
            # Note: We'll filter by segment/clinic after getting the data since clinic names
            # in Employee model might not exactly match Clinic model names
            employees = Employee.objects.only(
                "department", "joined_date", "resignation_date", "clinic_name"
            )

            # Build clinic segment mapping for filtering (using existing logic)
            clinic_segment_map = {}
            valid_segments = set()

            if segments or clinics:
                # Get clinic name → segment map (same as doctors_chart)
                clinic_qs = Clinic.objects.exclude(name="Ceased Clinic / Codes").values(
                    "name", "segment", "code"
                )
                clinic_segment_map = {
                    normalize_clinic_name(c["name"]): c["segment"] or "Others"
                    for c in clinic_qs
                }

                # Build sets of valid segments and clinic codes
                if segments:
                    valid_segments.update(segments)
                if clinics:
                    # Also get segments for these clinic codes
                    for clinic in clinic_qs:
                        if clinic["code"] in clinics:
                            valid_segments.add(clinic["segment"] or "Others")

            chart_data = defaultdict(lambda: defaultdict(int))
            resignations_per_quarter = defaultdict(int)

            # --- Staff data by quarter ---
            for y in range(start_year, end_year + 1):
                for q in range(1, 5):
                    if q == 1:
                        snapshot_date = date(y, 3, 31)
                        quarter_start = date(y, 1, 1)
                    elif q == 2:
                        snapshot_date = date(y, 6, 30)
                        quarter_start = date(y, 4, 1)
                    elif q == 3:
                        snapshot_date = date(y, 9, 30)
                        quarter_start = date(y, 7, 1)
                    else:
                        snapshot_date = date(y, 12, 31)
                        quarter_start = date(y, 10, 1)

                    if snapshot_date > today:
                        continue

                    quarter_label = f"Q{q} {y}"

                    for emp in employees:
                        # Apply segment/clinic filter if specified
                        if segments or clinics:
                            emp_clinic_name = emp.clinic_name or ""
                            # Use the same logic as doctors_chart to match clinic to segment
                            employee_segment = match_clinic_segment(
                                emp_clinic_name, clinic_segment_map
                            )

                            # Check if this employee's segment/clinic matches the filter
                            segment_matches = (
                                not segments or employee_segment in valid_segments
                            )

                            # For clinic filter, check if employee's clinic matches any specified clinic codes
                            clinic_matches = True
                            if clinics:
                                clinic_matches = False
                                normalized_clinic_name = normalize_clinic_name(
                                    emp_clinic_name
                                )
                                for clinic_code in clinics:
                                    try:
                                        clinic_obj = Clinic.objects.get(
                                            code=clinic_code
                                        )
                                        if (
                                            normalize_clinic_name(clinic_obj.name)
                                            == normalized_clinic_name
                                        ):
                                            clinic_matches = True
                                            break
                                    except Clinic.DoesNotExist:
                                        continue

                            if not (segment_matches and clinic_matches):
                                continue

                        department = (emp.department or "").strip()
                        category = get_category(department)

                        if not emp.joined_date:
                            continue

                        joined_date = (
                            emp.joined_date
                            if isinstance(emp.joined_date, date)
                            else emp.joined_date.date()
                        )
                        resignation_date = None
                        if emp.resignation_date:
                            resignation_date = (
                                emp.resignation_date
                                if isinstance(emp.resignation_date, date)
                                else emp.resignation_date.date()
                            )

                        # Count resignation if it happened in this quarter
                        if (
                            resignation_date
                            and quarter_start <= resignation_date <= snapshot_date
                        ):
                            resignations_per_quarter[quarter_label] += 1

                        # Count only active employees
                        if joined_date > snapshot_date:
                            continue
                        if resignation_date and resignation_date <= snapshot_date:
                            continue

                        chart_data[quarter_label][category] += 1
                        chart_data[quarter_label]["total"] += 1

            # --- Revenue growth by quarter ---
            latest_batch_number = FinancialTransactionSnapshot.objects.latest(
                "batch_number"
            ).batch_number

            # Build base queryset for revenue
            revenue_queryset = FinancialTransactionSnapshot.objects.filter(
                batch_number=latest_batch_number,
                financial_statement_type="revenue",
                year__range=[start_year, end_year],
                amount__gt=0,
            )

            # Apply segment filter if provided
            if segments:
                revenue_queryset = revenue_queryset.filter(clinic__segment__in=segments)

            # Apply clinic filter if provided
            if clinics:
                revenue_queryset = revenue_queryset.filter(clinic__code__in=clinics)

            revenue_entries = (
                revenue_queryset.values("year", "month")
                .annotate(amount=Sum("amount"))
                .order_by("year", "month")
            )

            revenue_df = pd.DataFrame(list(revenue_entries))
            if not revenue_df.empty:
                # Apply currency conversion
                revenue_df["amount"] = (
                    revenue_df["amount"].astype(float) * conversion_rate
                )

                revenue_df["quarter"] = ((revenue_df["month"] - 1) // 3 + 1).astype(int)
                revenue_df["name"] = (
                    "Q"
                    + revenue_df["quarter"].astype(str)
                    + " "
                    + revenue_df["year"].astype(str)
                )

                revenue_df = (
                    revenue_df.groupby("name").agg({"amount": "sum"}).reset_index()
                )

                revenue_df["past_amount"] = revenue_df["amount"].shift(1)
                revenue_df["past_amount"].fillna(0, inplace=True)
                revenue_df["revenue_growth"] = revenue_df.apply(
                    lambda x: (
                        round(
                            (x["amount"] - x["past_amount"]) / x["past_amount"] * 100, 2
                        )
                        if x["past_amount"] != 0
                        else 0
                    ),
                    axis=1,
                )
                revenue_df.drop(columns=["amount", "past_amount"], inplace=True)

                revenue_dict = dict(
                    zip(revenue_df["name"], revenue_df["revenue_growth"])
                )
            else:
                revenue_dict = {}

            # --- Profit growth by quarter ---
            profit_entries = (
                FinancialTransactionSnapshot.objects.filter(
                    batch_number=latest_batch_number,
                    financial_statement_type="profit_before_tax",
                    year__range=[start_year, end_year],
                )
                .values("year", "month")
                .annotate(amount=Sum("amount"))
                .order_by("year", "month")
            )

            profit_df = pd.DataFrame(list(profit_entries))
            if not profit_df.empty:
                # Apply currency conversion
                profit_df["amount"] = (
                    profit_df["amount"].astype(float) * conversion_rate
                )

                profit_df["quarter"] = ((profit_df["month"] - 1) // 3 + 1).astype(int)
                profit_df["name"] = (
                    "Q"
                    + profit_df["quarter"].astype(str)
                    + " "
                    + profit_df["year"].astype(str)
                )

                profit_df = (
                    profit_df.groupby("name").agg({"amount": "sum"}).reset_index()
                )

                profit_df["past_amount"] = profit_df["amount"].shift(1)
                profit_df["past_amount"].fillna(0, inplace=True)
                profit_df["profit_growth"] = profit_df.apply(
                    lambda x: (
                        round(
                            (x["amount"] - x["past_amount"]) / x["past_amount"] * 100, 2
                        )
                        if x["past_amount"] != 0
                        else 0
                    ),
                    axis=1,
                )
                profit_df.drop(columns=["amount", "past_amount"], inplace=True)

                profit_dict = dict(zip(profit_df["name"], profit_df["profit_growth"]))
            else:
                profit_dict = {}

            # --- Combine staff + revenue + profit growth ---
            result = []
            all_categories = list(CATEGORY_MAP.keys())
            prev_headcount = None

            for y in range(start_year, end_year + 1):
                for q in range(1, 5):
                    if q == 1:
                        snapshot_date = date(y, 3, 31)
                    elif q == 2:
                        snapshot_date = date(y, 6, 30)
                    elif q == 3:
                        snapshot_date = date(y, 9, 30)
                    else:
                        snapshot_date = date(y, 12, 31)

                    if snapshot_date > today:
                        continue

                    quarter_label = f"Q{q} {y}"
                    row = {"name": quarter_label}
                    for cat in all_categories:
                        row[cat] = int(chart_data[quarter_label].get(cat, 0))

                    headcount = int(chart_data[quarter_label].get("total", 0))
                    resignations = resignations_per_quarter.get(quarter_label, 0)

                    # Turnover rate
                    if prev_headcount is not None:
                        avg_headcount = (prev_headcount + headcount) / 2
                    else:
                        avg_headcount = headcount

                    turnover_rate = (
                        (resignations / avg_headcount * 100) if avg_headcount > 0 else 0
                    )

                    row["total"] = headcount
                    row["turnover_rate"] = round(turnover_rate, 2)
                    row["revenue_growth"] = revenue_dict.get(quarter_label, 0.0)
                    row["profit_growth"] = profit_dict.get(quarter_label, 0.0)

                    result.append(row)
                    prev_headcount = headcount

            data = {"chart_data": result, "commentary": ""}
            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e),
                status_code=status.HTTP_400_BAD_REQUEST,
            )


CLINIC_SEGMENT_MAPPING = {
    "alpha healthcare international pte ltd": "Women's Health",
    "astra @ gleneagles": "Women's Health",
    "astra womens's specialists (jl) pte ltd": "Women's Health",
    "babies and children specialist clinic pte ltd": "Paediatrics",
    "children's clinic central pte ltd": "Paediatrics",
    "children's clinic international": "Paediatrics",
    "hsc cancer centre pte ltd": "Oncology",
    "kids clinic @ bishan pte ltd": "Paediatrics",
    "kids clinic @ punggol": "Paediatrics",
    "lifescan imaging @ farrer park": "Imaging",
    "lifescan imaging @ novena": "Imaging",
    "lifescan imaging @ paragon": "Imaging",
    "lifescan medical @ farrer park": "Others",
    "lifescan medical @ novena": "Others",
    "lifescan medical @ paragon": "Others",
    "lsc eye clinic": "Others",
    "smg aesthetic (downtown) pte ltd": "Aesthetics",
    "smg astra centre for women pte ltd": "Women's Health",
    "smg astra o&g pte ltd": "Women's Health",
    "smg heart centre pte ltd": "Others",
    "smg kids orthopaedic pte ltd": "Others",
    "smg o&g centre pte ltd": "Women's Health",
    "sw1 aesthetics": "Aesthetics",
    "sw1 plastic surgery pte ltd": "Aesthetics",
    "tck @ novena pte ltd": "Women's Health",
    "the breast clinic pte ltd": "Others",
    "the cancer centre pte ltd": "Oncology",
    "the dental studio @ bishan": "Others",
    "the dental studio @ oue": "Others",
    "the dental studio @ paragon": "Others",
    "the dental studio @ tai thong": "Others",
    "the obstetrics & gynaecology centre @ mt e orchard": "Women's Health",
    "the obstetrics & gynaecology centre @ novena": "Women's Health",
    "the women's specialist centre (hc) pte ltd": "Women's Health",
    "togc @ gleneagles pte ltd": "Women's Health",
    "vidaskin pte ltd": "Aesthetics",
    "wellness & gynaecology centre pte ltd": "Women's Health",
}


def get_segment_from_mapping(clinic_name):
    return CLINIC_SEGMENT_MAPPING.get(clinic_name.lower(), "Others")


def normalize_clinic_name(name: str) -> str:
    if not name:
        return ""

    name = name.lower()
    name = re.sub(r"[^\w\s]", " ", name)
    name = re.sub(r"\b(at|@)\b", " at ", name)
    name = re.sub(r"\s+", " ", name).strip()

    replacements = {
        "togc": "the obstetrics gynaecology centre",
        "smg": "",
        "centre": "center",
        "centres": "centers",
        "womens": "women's",
        "kid's": "kids",
        "tai thong": "tai tong",
    }
    for old, new in replacements.items():
        name = name.replace(old, new)

    return name.strip()


def match_clinic_segment(name, normalized_to_segment):
    # Step 1: Try exact match from hardcoded mapping
    mapped_segment = get_segment_from_mapping(name)
    if mapped_segment != "Others":
        return mapped_segment

    # Step 2: Fallback to normalized fuzzy DB match
    norm_name = normalize_clinic_name(name)
    match_result = process.extractOne(
        norm_name, normalized_to_segment.keys(), scorer=fuzz.token_sort_ratio
    )
    if match_result:
        match_name, score = match_result
        if score >= 70:
            return normalized_to_segment[match_name]

    return "Others"


class PatientsViewSet(ModelViewSet):
    @action(detail=False, methods=["get"], url_path="clinics")
    def clinics(self, request):
        try:
            clinics = sorted(CLINIC_DB_MAPPING.keys())

            return custom_success_response(clinics)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="patients/chart")
    def patients_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            # Build base queryset for revenue
            revenue_queryset = JournalEntryTransaction.objects.filter(
                chart_of_account__account_type__in=["Revenues", "Income"],
                transaction_date__year__gte=start_year,
                transaction_date__year__lte=end_year,
            ).exclude(memo__icontains="FOR CLOSING")

            # Apply segment filter if provided
            if segments:
                # Get clinic codes for the specified segments
                clinic_codes = Clinic.objects.filter(segment__in=segments).values_list(
                    "code", flat=True
                )
                revenue_queryset = revenue_queryset.filter(
                    chart_of_account__clinic_code__in=clinic_codes
                )

            # Apply clinic filter if provided
            if clinics:
                revenue_queryset = revenue_queryset.filter(
                    chart_of_account__clinic_code__in=clinics
                )

            # ==== Step 1: Revenue per year ====
            revenue_qs = (
                revenue_queryset.annotate(year=ExtractYear("transaction_date"))
                .values("year")
                .annotate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
            )

            revenue_by_year = {
                row["year"]: float(row["total_credit"] - row["total_debit"])
                * conversion_rate
                for row in revenue_qs
            }

            # ==== Step 2: Patient count per year ====
            patients_qs = (
                Patients.objects.filter(
                    created_on__year__gte=start_year,
                    created_on__year__lte=end_year,
                )
                .annotate(year=ExtractYear("created_on"))
                .values("year")
                .annotate(count=Count("id"))
            )

            patients_by_year = {row["year"]: row["count"] for row in patients_qs}

            # ==== Step 3: Build response ====
            chart_data = []
            for y in range(start_year, end_year + 1):
                patient_count = patients_by_year.get(y, 0)
                total_revenue = revenue_by_year.get(y, 0)
                revenue_per_patient = (
                    round(total_revenue / patient_count, 2) if patient_count else 0.0
                )

                chart_data.append(
                    {
                        "name": str(y),
                        "barValue": patient_count,
                        "lineValue": revenue_per_patient,
                    }
                )

            data = {"chart_data": chart_data, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="new-patients/chart")
    def new_patients_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # ==== Step 1: Revenue per year ====
            revenue_by_year = dict(
                JournalEntryTransaction.objects.filter(
                    chart_of_account__account_type__in=["Revenues", "Income"],
                    transaction_date__year__gte=start_year,
                    transaction_date__year__lte=end_year,
                )
                .exclude(memo__icontains="FOR CLOSING")
                .annotate(year=ExtractYear("transaction_date"))
                .values("year")
                .annotate(
                    revenue=Coalesce(
                        Sum("reporting_credit_amount") - Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    )
                )
                .values_list("year", "revenue")
            )

            # ==== Step 2: New patients per year (joined with first appointment) ====
            first_appt_subquery = (
                Appointments.objects.filter(patient_id=OuterRef("patient_id"))
                .order_by("appointment_date")
                .values("appointment_date")[:1]
            )

            new_patients_qs = (
                Patients.objects.annotate(
                    first_appt_date=Subquery(
                        first_appt_subquery, output_field=DateField()
                    ),
                    created_year=ExtractYear("created_on"),
                    first_year=ExtractYear(
                        Subquery(first_appt_subquery, output_field=DateField())
                    ),
                )
                .filter(
                    created_year=F("first_year"),
                    created_year__gte=start_year,
                    created_year__lte=end_year,
                )
                .values("created_year")
                .annotate(count=Count("id"))
            )

            new_patients_by_year = {
                row["created_year"]: row["count"] for row in new_patients_qs
            }

            # ==== Step 3: Build chart data ====
            chart_data = []
            for y in range(start_year, end_year + 1):
                patient_count = new_patients_by_year.get(y, 0)
                revenue = float(revenue_by_year.get(y, 0) or 0) * conversion_rate
                per_patient = (
                    round(revenue / patient_count, 2) if patient_count else 0.0
                )

                chart_data.append(
                    {
                        "name": str(y),
                        "barValue": patient_count,
                        "lineValue": per_patient,
                    }
                )

            data = {"chart_data": chart_data, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="digital-mkt/chart")
    def digital_mkt_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            # === Step 1: Revenue by year ===
            revenue_by_year = dict(
                JournalEntryTransaction.objects.filter(
                    chart_of_account__account_type__in=["Revenues", "Income"],
                    transaction_date__year__gte=start_year,
                    transaction_date__year__lte=end_year,
                )
                .exclude(memo__icontains="FOR CLOSING")
                .annotate(year=ExtractYear("transaction_date"))
                .values("year")
                .annotate(
                    total_credit=Coalesce(Sum("reporting_credit_amount"), Value(0)),
                    total_debit=Coalesce(Sum("reporting_debit_amount"), Value(0)),
                    revenue=ExpressionWrapper(
                        F("total_credit") - F("total_debit"),
                        output_field=DecimalField(),
                    ),
                )
                .values_list("year", "revenue")
            )

            # === Step 2: Digital Marketing Patients (Join via Subquery) ===
            dm_patients_qs = (
                Patients.objects.filter(
                    created_on__year__gte=start_year,
                    created_on__year__lte=end_year,
                    patient_id__in=Subquery(
                        PatientMKT.objects.filter(
                            patient_id=OuterRef("patient_id")
                        ).values("patient_id")
                    ),
                )
                .annotate(year=ExtractYear("created_on"))
                .values("year")
                .annotate(count=Count("id"))
            )

            patients_by_year = {row["year"]: row["count"] for row in dm_patients_qs}

            # === Step 3: Build Chart Data ===
            chart_data = []
            for y in range(start_year, end_year + 1):
                patient_count = patients_by_year.get(y, 0)
                revenue = float(revenue_by_year.get(y, 0) or 0) * conversion_rate
                per_patient = (
                    round(revenue / patient_count, 2) if patient_count else 0.0
                )

                chart_data.append(
                    {
                        "name": str(y),
                        "barValue": patient_count,
                        "lineValue": per_patient,
                    }
                )

            data = {"chart_data": chart_data, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="patient-count-by-clinic/chart")
    def patient_count_by_clinic_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            start_year = end_year - last_n_years

            clinic_name = request.query_params.get("clinic", "")

            if not clinic_name:
                raise ValueError("Clinic is required")

            clinic_codes = CLINIC_DB_MAPPING.get(clinic_name)
            if not clinic_codes:
                raise ValueError(f"Clinic '{clinic_name}' not found in mapping")

            # Build Q filters
            code_filter = Q()
            for code in clinic_codes:
                code_filter |= Q(appointment_id__startswith=code)

            order_code_filter = Q()
            for code in clinic_codes:
                order_code_filter |= Q(order_id__startswith=code)

            today = datetime.today().date()

            # --- PATIENT COUNTS ---
            appointments = (
                Appointments.objects.filter(
                    appointment_date__year__gte=start_year,
                    appointment_date__year__lte=end_year,
                    appointment_date__lte=today,
                )
                .filter(code_filter)
                .values("appointment_date__year", "appointment_date__month")
                .annotate(patient=Count("patient_id", distinct=True))
            )

            # --- REVENUE ---
            sales = (
                SalesOrder.objects.filter(
                    date__year__gte=start_year,
                    date__year__lte=end_year,
                    date__lte=today,
                )
                .filter(order_code_filter)
                .values("date__year", "date__month")
                .annotate(revenue=Sum("total"))
            )

            # Convert to dicts for lookup
            patient_lookup = {
                (a["appointment_date__year"], a["appointment_date__month"]): a[
                    "patient"
                ]
                for a in appointments
            }
            revenue_lookup = {
                (s["date__year"], s["date__month"]): float(s["revenue"] or 0.0)
                for s in sales
            }

            # Merge results and calculate revenue per patient
            all_keys = sorted(set(patient_lookup.keys()) | set(revenue_lookup.keys()))
            chart_data = []
            for year, month in all_keys:
                patient_count = patient_lookup.get((year, month), 0)
                total_revenue = revenue_lookup.get((year, month), 0.0) * conversion_rate
                revenue_per_patient = (
                    total_revenue / patient_count if patient_count else 0.0
                )

                chart_data.append(
                    {
                        "year": year,
                        "month": month,
                        "clinic": clinic_name,
                        "patient": patient_count,
                        "revenue": revenue_per_patient,
                    }
                )

            data = {"chart_data": chart_data, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="patient-visit-by-clinic/chart")
    def patient_visit_by_clinic_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = 1
            start_year = end_year - last_n_years

            clinic_name = request.query_params.get("clinic", "")

            if not clinic_name:
                raise ValueError("Clinic is required")

            clinic_codes = CLINIC_DB_MAPPING.get(clinic_name)
            if not clinic_codes:
                raise ValueError(f"Clinic '{clinic_name}' not found in mapping")

            # Build Q filters
            code_filter = Q()
            for code in clinic_codes:
                code_filter |= Q(appointment_id__startswith=code)

            order_code_filter = Q()
            for code in clinic_codes:
                order_code_filter |= Q(order_id__startswith=code)

            today = datetime.today().date()

            # --- VISITS (still returned as "patient") ---
            # Step 1: unique (patient_id, appointment_date)
            unique_visits = (
                Appointments.objects.filter(
                    appointment_date__year__gte=start_year,
                    appointment_date__year__lte=end_year,
                    appointment_date__lte=today,
                )
                .filter(code_filter)
                .values(
                    "appointment_date__year",
                    "appointment_date__month",
                    "patient_id",
                    "appointment_date",
                )
                .distinct()
            )

            # Step 2: count per month
            visits = unique_visits.values(
                "appointment_date__year", "appointment_date__month"
            ).annotate(patient=Count("patient_id"))

            # --- REVENUE ---
            sales = (
                SalesOrder.objects.filter(
                    date__year__gte=start_year,
                    date__year__lte=end_year,
                    date__lte=today,
                )
                .filter(order_code_filter)
                .values("date__year", "date__month")
                .annotate(revenue=Sum("total"))
            )

            # Convert to dicts for lookup
            patient_lookup = {
                (v["appointment_date__year"], v["appointment_date__month"]): v[
                    "patient"
                ]
                for v in visits
            }
            revenue_lookup = {
                (s["date__year"], s["date__month"]): float(s["revenue"] or 0.0)
                for s in sales
            }

            # Merge results and calculate revenue per patient (visit)
            all_keys = sorted(set(patient_lookup.keys()) | set(revenue_lookup.keys()))
            chart_data = []
            for year, month in all_keys:
                patient_count = patient_lookup.get((year, month), 0)
                total_revenue = revenue_lookup.get((year, month), 0.0) * conversion_rate
                revenue_per_patient = (
                    total_revenue / patient_count if patient_count else 0.0
                )

                chart_data.append(
                    {
                        "year": year,
                        "month": month,
                        "clinic": clinic_name,
                        "patient": patient_count,  # visit count, but still called "patient"
                        "revenue": revenue_per_patient,
                    }
                )

            data = {"chart_data": chart_data, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], url_path="new-patient-count-by-clinic/chart")
    def new_patient_count_by_clinic_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD").upper()
            conversion_rate = get_conversion_rate(currency)

            segments = request.query_params.getlist("segment", None)
            clinics = request.query_params.getlist("clinic", None)

            end_year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = 1
            start_year = end_year - last_n_years

            clinic_name = request.query_params.get("clinic", "")

            if not clinic_name:
                raise ValueError("Clinic is required")

            clinic_codes = CLINIC_DB_MAPPING.get(clinic_name)
            if not clinic_codes:
                raise ValueError(f"Clinic '{clinic_name}' not found in mapping")

            # Build Q filters
            code_filter = Q()
            for code in clinic_codes:
                code_filter |= Q(appointment_id__startswith=code)

            order_code_filter = Q()
            for code in clinic_codes:
                order_code_filter |= Q(order_id__startswith=code)

            today = datetime.today().date()

            # --- Step 1: Find each patient's absolute first visit date ---
            first_appt_subquery = (
                Appointments.objects.filter(
                    patient_id=OuterRef("patient_id"),
                    appointment_date__lte=today,
                )
                .filter(code_filter)
                .order_by("appointment_date")
                .values("appointment_date")[:1]
            )

            earliest_appointments = (
                Appointments.objects.filter(
                    appointment_date__lte=today,
                )
                .filter(code_filter)
                .values("patient_id")
                .annotate(
                    first_visit=Subquery(first_appt_subquery, output_field=DateField())
                )
                .values("patient_id", "first_visit")
            )

            # Dict: normalized patient_id -> (first_visit_year, first_visit_month)
            patient_first_visits = {}
            for ea in earliest_appointments:
                if (
                    ea["first_visit"]
                    and start_year <= ea["first_visit"].year <= end_year
                ):
                    normalized_id = ea["patient_id"].split("_")[-1]
                    patient_first_visits[normalized_id] = (
                        ea["first_visit"].year,
                        ea["first_visit"].month,
                    )

            # --- Step 2: Count new patients grouped by first visit year/month ---
            patient_lookup = {}
            for _, (y, m) in patient_first_visits.items():
                key = (y, m)
                patient_lookup[key] = patient_lookup.get(key, 0) + 1

            # --- Step 3: Revenue from new patients in their first-visit month only ---
            # Get all sales from new patients in reporting window
            all_sales = (
                SalesOrder.objects.filter(
                    patient_id__in=patient_first_visits.keys(),
                    date__year__gte=start_year,
                    date__year__lte=end_year,
                    date__lte=today,
                )
                .filter(order_code_filter)
                .values("patient_id", "date__year", "date__month")
                .annotate(total=Sum("total"))
            )

            revenue_lookup = {}
            for s in all_sales:
                pid = s["patient_id"].split("_")[-1]  # normalize ID
                fv_y, fv_m = patient_first_visits.get(pid, (None, None))

                # Only count if this sale is in the first-visit month
                if fv_y == s["date__year"] and fv_m == s["date__month"]:
                    revenue_lookup[(fv_y, fv_m)] = revenue_lookup.get(
                        (fv_y, fv_m), 0.0
                    ) + float(s["total"] or 0.0)

            # --- Step 4: Merge results ---
            all_keys = sorted(set(patient_lookup.keys()) | set(revenue_lookup.keys()))
            chart_data = []
            for y, m in all_keys:
                patient_count = patient_lookup.get((y, m), 0)
                total_revenue = revenue_lookup.get((y, m), 0.0) * conversion_rate
                revenue_per_patient = (
                    total_revenue / patient_count if patient_count else 0.0
                )

                chart_data.append(
                    {
                        "year": y,
                        "month": m,
                        "clinic": clinic_name,
                        "patient": patient_count,  # new patients only
                        "revenue": revenue_per_patient,  # avg revenue (month total ÷ new patients)
                    }
                )

            data = {"chart_data": chart_data, "commentary": ""}

            return custom_success_response(data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )


CLINIC_DB_MAPPING = {
    "Lifescan Imaging Novena": ["drnovrad"],
    "Lifescan Imaging Paragon": ["drlsi"],
    "Lifescan Physio": ["smg_lsphysio"],
    "Astra Women's Specialists Centre - Novena": ["drastra", "drastrajl"],
    "Astra Women's Specialists Centre - Bishan": ["drhenrycheng", "drjameslee"],
    "Astra Women's Specialists - Jurong East": ["drastrajurong253"],
    "TOGC Gleneagles": ["drtogcglen0819"],
    "Astra Women's Specialists Centre - TPY": ["drastratoapayoh"],
    "Lifescan Medical & Wellness": ["drlsm"],
    "SMG Urology Centre": ["drsugcfp"],
    "Children's Clinic International": ["smg_kidsclinicnov"],
    "Kids Clinic @ Punggol": ["smg_kidsclinicpgl"],
    "Kids Clinic @ TPN": ["smg_kidsclinictam"],
    "Kids Clinic @ SengKang": ["smg_kidsclinicbdk"],
    "Kids Clinic @ Mt Alvernia": ["smg_kidsclinicmta"],
    "Astra Women's Specialists - Orchard": ["smg_sogcmeo", "smg_togcmeo"],
    "Bone Island Children's Clinic": ["smg_kidsmtaortho"],
    "Cardiac Centre": ["smg_cci"],
    "The Cancer Centre": ["drharleyonco", "SMG_TCC"],
    "SW1 Plastic": ["SW1PLASTIC"],
    "The Breast Clinic": ["SMG_TBC"],
    "Wellness & Gynaecology Centre": ["SMG_WGC"],
    "LSC Eye Clinic": ["SMG_LSCE"],
    "Skin Pple Clinic @OUE": ["SMG_AESTHETIC"],
    "SW1 Paragon": ["SW1"],
    "Lifescan Imaging Farrer Park": ["SMG_LSI"],
    "Kids Clinic @ Bishan": ["KCB"],
    "The Dental Studio @ Paragon": ["SEMR_Paragon"],
    "The Dental Studio @ OUE": ["SEMR_OUE"],
    "The Dental Studio @ Tai Tong": ["SEMR_TaiThong"],
}
